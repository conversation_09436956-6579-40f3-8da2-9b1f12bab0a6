<template>
	<!-- 登录页面的主容器 -->
	<div class="w3l-signinform">
		<!-- 页面内容的包装容器 -->
		<div class="wrapper">
			<!-- 主要内容区域 -->
			<div class="w3l-form-info">
				<div class="w3_info">
					<!-- 标题和欢迎语 -->
					<h1>欢迎回来</h1>
					<p class="sub-para">欢迎使用我们的服务，请登录您的账户</p>
					<h2>登录</h2>

					<!-- 登录表单 -->
					<form @submit.prevent="login" class="form">
						<!-- 用户名输入框 -->
						<div class="input-group">
							<!--							<span><i class="fa fa-user" aria-hidden="true"></i></span>-->
							<input v-model="loginForm.username" type="text" id="username" placeholder="请输入用户名" required />
						</div>

						<!-- 密码输入框 -->
						<div class="input-group two-groop">
							<!--							<span><i class="fa fa-key" aria-hidden="true"></i></span>-->
							<input v-model="loginForm.password" type="password" id="password" placeholder="请输入密码" required />
						</div>

						<!-- 忘记密码链接 -->
						<div class="form-row bottom">
							<!--							<input v-model="rememberMe" type="checkbox" id="remember" name="remember" value="remember" />-->
							<!--							<label for="remember"> 记住我</label>-->
							<!--													<a style="cursor: pointer" class="forgot">忘记密码？</a>-->
							<a href="#" class="forgot-password">忘记密码？</a>
						</div>

						<!-- 登录按钮 -->
						<button class="login-button" type="submit">登录</button>
					</form>

					<!-- 注册提示 -->
					<p class="account">没有账号？ <a @click="toRegister()" style="cursor: pointer">立即注册</a></p>

					<!-- 社交账户登录 -->
					<!--					<div class="social-account-container">-->
					<!--						<span class="title">或者使用社交账户登录</span>-->
					<!--						<div class="social-accounts">-->
					<!--							<button class="social-button google">-->
					<!--								<svg class="svg" xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 488 512">-->
					<!--									<path-->
					<!--										d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"-->
					<!--									></path>-->
					<!--								</svg>-->
					<!--							</button>-->
					<!--							<button class="social-button apple">-->
					<!--								<svg class="svg" xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 384 512">-->
					<!--									<path-->
					<!--										d="M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z"-->
					<!--									></path>-->
					<!--								</svg>-->
					<!--							</button>-->
					<!--							<button class="social-button twitter">-->
					<!--								<svg class="svg" xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512">-->
					<!--									<path-->
					<!--										d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"-->
					<!--									></path>-->
					<!--								</svg>-->
					<!--							</button>-->
					<!--						</div>-->
					<!--					</div>-->
				</div>
			</div>
		</div>

		<!-- 页脚部分 -->
	</div>
</template>
<script setup>
import Cookies from 'js-cookie';
import { reactive, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import axios from 'axios';

const router = useRouter();

const loginForm = reactive({
	username: '',
	password: '',
});

// Remember me state
const rememberMe = ref(false);
onMounted(() => {
	const savedRememberMe = localStorage.getItem('rememberMe');
	if (savedRememberMe) {
		rememberMe.value = localStorage.getItem('rememberMe');
		loginForm.username = localStorage.getItem('username');
		loginForm.password = localStorage.getItem('password');
	}
});

// Login function
const login = async () => {
	const res = await axios.post('/api/login/', {
		username: loginForm.username,
		password: loginForm.password,
	});
	if (res.data.message === '登录成功') {
		ElMessage.success('登录成功');
		Cookies.set('user', JSON.stringify(res.data.user), { expires: 3650 });
		if (rememberMe.value) {
			localStorage.setItem('rememberMe', rememberMe.value);
			localStorage.setItem('username', loginForm.username);
			localStorage.setItem('password', loginForm.password);
		}
		router.push({ path: '/main' });
	} else if (res.data.message === '用户不存在') {
		ElMessage.error('用户不存在');
	} else if (res.data.message === '密码错误') {
		ElMessage.error('密码错误');
	} else {
		ElMessage.error('登录失败');
	}
};

// Redirect to registration page
const toRegister = () => {
	router.push({ path: '/register' });
};
</script>
<style scoped>
/* Add your CSS here */
/* Ensure the form container has proper padding and alignment */
/* Main container */
.w3l-signinform {
	padding: 40px;
	min-height: 100vh;
	min-width: 100vw;
	background: url('./bg.jpg') no-repeat center;
	background-size: cover;
	-webkit-background-size: cover;
	-o-background-size: cover;
	-moz-background-size: cover;
	-ms-background-size: cover;
	position: relative;
	z-index: 1;
	display: flex;
	justify-content: center;
	align-items: center;
}

/* Login form container */
.w3_info {
	background-color: white; /* Keep the login form's background white */
	max-width: 450px;
	width: 100%;
	padding: 25px 55px;
	border-radius: 20px;
	box-shadow: rgba(133, 189, 215, 0.**********) 0px 20px 30px -10px;
}

/* Other styling remains the same */
h1,
h2 {
	font-weight: 900;
	color: rgb(16, 137, 211);
}

h1 {
	font-size: 36px;
	margin-bottom: 0.4em;
}

h2 {
	font-size: 24px;
	margin-top: 20px;
}

.sub-para {
	font-size: 16px;
	margin-bottom: 20px;
	color: #555;
}

.input-group {
	position: relative;
	margin-top: 20px;
}

.input-group input {
	width: 100%;
	background: white;
	border: none;
	padding: 15px;
	border-radius: 20px;
	box-shadow: #cff0ff 0px 10px 10px -5px;
	border-inline: 2px solid transparent;
}

.input-group input:focus {
	outline: none;
	border-inline: 2px solid #12b1d1;
}

.forgot-password {
	display: block;
	margin-top: 10px;
	font-size: 13px;
	color: rgb(139, 189, 231);
	text-decoration: none;
}

.forgot-password:hover {
	text-decoration: underline;
}

.account {
	margin-top: 15px;
	font-size: 14px;
}

.account a {
	color: rgb(16, 137, 211);
}

.account a:hover {
	text-decoration: underline;
}

.login-button {
	width: 100%;
	font-weight: bold;
	background: linear-gradient(45deg, rgb(16, 137, 211) 0%, rgb(18, 177, 209) 100%);
	color: white;
	padding: 15px;
	margin: 20px 0;
	border-radius: 20px;
	box-shadow: rgba(133, 189, 215, 0.**********) 0px 20px 10px -15px;
	border: none;
	cursor: pointer;
	transition: all 0.2s ease-in-out;
}

.login-button:hover {
	transform: scale(1.03);
	box-shadow: rgba(133, 189, 215, 0.**********) 0px 23px 10px -20px;
}

.login-button:active {
	transform: scale(0.95);
	box-shadow: rgba(133, 189, 215, 0.**********) 0px 15px 10px -10px;
}

.social-accounts {
	display: flex;
	justify-content: center;
	gap: 15px;
	margin-top: 25px;
}

.social-button {
	width: 45px;
	height: 45px;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 1.3rem;
	cursor: pointer;
	transition: all 0.2s ease-in-out;
}

.social-button svg {
	width: 30px;
	height: 30px;
}

.google {
	background-color: #4285f4;
}

.apple {
	background-color: #000000;
}

.twitter {
	background-color: #00acee;
}

.social-button:hover {
	transform: scale(1.1);
}

.social-button:active {
	transform: scale(0.9);
}

.title {
	font-size: 14px;
	color: rgb(109, 109, 109);
	margin-top: 20px;
}
</style>
