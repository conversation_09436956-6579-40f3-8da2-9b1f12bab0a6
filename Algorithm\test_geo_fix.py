#!/usr/bin/env python3
"""
测试地理信息模块的%{text}显示问题修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from geo_analysis import generate_geo_data, create_interactive_map

def test_hover_text_fix():
    """测试悬停文本修复"""
    print("🧪 测试地理信息模块的悬停文本修复...")
    
    try:
        # 生成测试数据
        print("📊 生成测试数据...")
        stations_df, sources_df = generate_geo_data()
        print(f"✅ 数据生成成功: {len(stations_df)}个监测站, {len(sources_df)}个污染源")
        
        # 创建交互式地图
        print("🗺️ 创建交互式地图...")
        map_fig = create_interactive_map(stations_df, sources_df, 'PM2.5')
        print("✅ 地图创建成功")
        
        # 检查图表配置
        print("🔍 检查图表配置...")
        traces = map_fig.data
        
        for i, trace in enumerate(traces):
            print(f"  Trace {i+1}:")
            print(f"    - 类型: {trace.type}")
            print(f"    - 名称: {trace.name}")
            print(f"    - hoverinfo: {getattr(trace, 'hoverinfo', 'None')}")
            print(f"    - hovertemplate: {getattr(trace, 'hovertemplate', 'None')}")
            
            # 检查是否还有%{text}问题
            if hasattr(trace, 'hovertemplate') and trace.hovertemplate:
                if '%{text}' in str(trace.hovertemplate):
                    print(f"    ⚠️  发现%{{text}}模板")
                else:
                    print(f"    ✅ 悬停模板正常")
            
            if hasattr(trace, 'text') and trace.text:
                if isinstance(trace.text, list) and len(trace.text) > 0:
                    sample_text = str(trace.text[0])[:100]
                    print(f"    - 文本样本: {sample_text}...")
        
        # 保存测试图表
        print("💾 保存测试图表...")
        map_fig.write_html("test_geo_fix.html")
        print("✅ 测试图表已保存为 test_geo_fix.html")
        
        print("\n🎉 测试完成！")
        print("💡 提示: 打开 test_geo_fix.html 查看修复效果")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_hover_text_fix()
    if success:
        print("\n✅ 所有测试通过！%{text}显示问题已修复。")
    else:
        print("\n❌ 测试失败，请检查错误信息。")
