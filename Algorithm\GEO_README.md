# 🗺️ 地理信息数据分析模块

## 📋 模块概述

地理信息数据分析模块是空气污染数据分析系统的重要组成部分，提供污染源和监测站的地理位置可视化，支持空间分析和多维度数据展示。

## ✨ 主要功能

### 1. 交互式地图 🗺️
- **监测站分布**：显示所有空气质量监测站的地理位置
- **污染源标记**：标注各类污染源（工厂、发电厂等）的位置
- **实时数据**：点击标记查看实时污染物浓度数据
- **颜色编码**：根据空气质量等级使用不同颜色标识

### 2. 污染物热力图 🌡️
- **浓度分布**：以热力图形式展示污染物浓度分布
- **多污染物支持**：支持PM2.5、PM10、SO2、NO2、CO、O3等
- **统计分析**：提供污染物浓度的统计描述
- **分布直方图**：显示污染物浓度的分布情况

### 3. 空间统计分析 📊
- **基础统计**：监测站数量、污染源数量等基本信息
- **空气质量分析**：优良率计算、污染等级分布
- **可视化图表**：饼图、柱状图等多种图表展示

### 4. 污染源分析 🏭
- **类型分布**：不同类型污染源的数量统计
- **排放等级**：污染源排放等级分析
- **详细信息**：污染源的详细属性信息表

## 🚀 快速开始

### 方法一：直接运行地理信息模块
```bash
# 进入项目目录
cd Algorithm

# 运行地理信息分析模块
python run_geo_app.py
```

### 方法二：通过主应用访问
```bash
# 运行主应用
streamlit run main_app.py

# 在侧边栏选择"🗺️ 地理信息分析"
```

### 方法三：单独运行
```bash
# 直接运行地理信息可视化
streamlit run geo_visualization.py --server.port 8502
```

## 📦 依赖包

### 必需依赖
```
streamlit>=1.25.0
pandas>=2.0.0
numpy>=1.24.0
plotly>=5.15.0
folium>=0.14.0
streamlit-folium>=0.13.0
```

### 可选依赖（用于真实地理数据）
```
geopandas>=0.13.0
shapely>=2.0.0
```

## 🛠️ 安装依赖

### 自动安装
运行启动脚本时会自动检查并安装缺失的依赖包：
```bash
python run_geo_app.py
```

### 手动安装
```bash
pip install streamlit pandas numpy plotly folium streamlit-folium
```

## 📊 数据格式

### 监测站数据格式
```json
{
    "id": "MS_001",
    "name": "东城监测站",
    "latitude": 39.9042,
    "longitude": 116.4074,
    "PM2.5": 45.2,
    "PM10": 78.5,
    "SO2": 12.3,
    "NO2": 35.7,
    "CO": 1.2,
    "O3": 65.8,
    "AQI_level": "良",
    "last_update": "2023-12-01 14:30:00"
}
```

### 污染源数据格式
```json
{
    "id": "PS_001",
    "name": "工厂_1",
    "latitude": 39.8842,
    "longitude": 116.3874,
    "source_type": "工厂",
    "emission_level": "中",
    "daily_emission": 125.5,
    "established_year": 1995
}
```

## 🎯 使用说明

### 1. 控制面板操作
- **分析类型选择**：在侧边栏选择不同的分析类型
- **污染物选择**：选择要分析的污染物类型
- **数据过滤**：根据AQI等级和污染源类型过滤数据

### 2. 地图交互
- **缩放**：使用鼠标滚轮或地图控件缩放
- **平移**：拖拽地图进行平移
- **点击标记**：点击监测站或污染源查看详细信息
- **图例**：参考地图底部的图例理解标记含义

### 3. 数据导出
- 图表可以通过Plotly的工具栏导出为PNG、SVG等格式
- 数据表格支持复制和下载功能

## 🔧 自定义配置

### 修改地图中心点
在 `geo_analysis.py` 中修改：
```python
center_lat, center_lon = 39.9042, 116.4074  # 北京
```

### 添加新的污染源类型
在 `generate_geo_data()` 函数中修改：
```python
source_types = ['工厂', '发电厂', '化工厂', '钢铁厂', '水泥厂', '炼油厂', '新类型']
```

### 自定义颜色方案
在 `create_interactive_map()` 函数中修改颜色映射。

## 🐛 常见问题

### Q1: 地图无法显示
**A**: 检查网络连接，确保可以访问OpenStreetMap瓦片服务。

### Q2: 依赖包安装失败
**A**: 尝试使用国内镜像源：
```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ folium streamlit-folium
```

### Q3: 数据加载缓慢
**A**: 数据已使用Streamlit缓存机制，首次加载后会显著提速。

## 📈 扩展功能

### 支持真实地理数据
1. 安装geopandas：`pip install geopandas`
2. 准备Shapefile或GeoJSON格式的数据文件
3. 修改数据加载函数以读取真实数据

### 添加时间序列分析
可以扩展模块以支持污染物浓度的时间变化分析。

### 集成天气数据
可以添加天气数据图层，分析天气对污染的影响。

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 创建Issue在项目仓库
- 查看项目文档获取更多信息

---

**注意**：本模块使用模拟数据进行演示，实际使用时请替换为真实的监测数据。
