@echo off
title Vue 项目启动脚本
echo ========================================
echo  检查并创建 node_modules 软链接...
echo ========================================

REM 以管理员权限重新运行
:: 判断是否以管理员权限运行
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 需要以管理员权限运行本脚本...
    echo 正在尝试重新以管理员权限运行...
    powershell -Command "Start-Process cmd -ArgumentList '/c \"%~f0\"' -Verb RunAs"
    exit /b
)

REM 切换到当前 bat 文件所在目录
cd /d %~dp0

REM 检查 node_modules 是否存在
if exist "node_modules" (
    echo node_modules 软链接已存在，无需创建。
) else (
    echo 创建软链接到 E:\vue-env\node_modules...
    mklink /D "node_modules" "E:\vue-env\node_modules"
    echo 软链接创建完成！
)

echo ========================================
echo  启动 Vue 项目...
echo ========================================

REM 确保安装依赖（可选）


REM 启动 Vue 开发服务器
npm run dev

REM 保持窗口打开，防止自动关闭
pause
