#!/usr/bin/env python3
"""
简单地图测试脚本 - 基础版本
不依赖外部库，用于验证Python环境和基本功能
"""

import sys
import os
import webbrowser
from datetime import datetime

def check_python_version():
    """检查Python版本"""
    print("🐍 Python版本检查...")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_dependencies():
    """检查依赖是否可用"""
    print("\n🔍 检查依赖...")
    
    required_packages = ['plotly', 'pandas', 'numpy']
    available_packages = []
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
            available_packages.append(package)
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺失的包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False, missing_packages
    else:
        print("\n✅ 所有依赖都已安装")
        return True, []

def create_basic_html_map():
    """创建基础HTML地图（不依赖plotly）"""
    print("\n🗺️ 创建基础HTML地图...")
    
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>基础地图测试</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #map { height: 500px; width: 100%; border: 1px solid #ccc; }
        .info { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🗺️ 基础地图渲染测试</h1>
    <div class="info">
        <strong>测试信息:</strong><br>
        生成时间: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """<br>
        地图类型: 基础交互式地图<br>
        技术栈: Leaflet.js + OpenStreetMap
    </div>
    
    <div id="map"></div>
    
    <div class="info">
        <strong>功能说明:</strong><br>
        • 可以拖拽移动地图<br>
        • 可以缩放地图<br>
        • 显示北京市中心及测试点<br>
        • 点击标记查看信息
    </div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        var map = L.map('map').setView([39.9042, 116.4074], 10);

        // 添加地图图层
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // 添加标记点
        var markers = [
            {lat: 39.9042, lng: 116.4074, title: "北京市中心", desc: "天安门广场附近"},
            {lat: 39.9142, lng: 116.4174, title: "测试点1", desc: "PM2.5: 45 μg/m³"},
            {lat: 39.8942, lng: 116.3974, title: "测试点2", desc: "PM2.5: 78 μg/m³"},
            {lat: 39.9242, lng: 116.4274, title: "测试点3", desc: "PM2.5: 32 μg/m³"},
            {lat: 39.8842, lng: 116.3874, title: "测试点4", desc: "PM2.5: 89 μg/m³"}
        ];

        // 添加标记到地图
        markers.forEach(function(marker) {
            L.marker([marker.lat, marker.lng])
                .addTo(map)
                .bindPopup('<b>' + marker.title + '</b><br>' + marker.desc);
        });

        // 添加圆圈表示影响范围
        L.circle([39.9042, 116.4074], {
            color: 'red',
            fillColor: '#f03',
            fillOpacity: 0.2,
            radius: 5000
        }).addTo(map).bindPopup("北京市中心区域");

        console.log("地图初始化完成！");
    </script>
</body>
</html>
"""
    
    filename = "basic_html_map_test.html"
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"✅ 基础HTML地图已保存为: {filename}")
        return filename
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return None

def create_test_report():
    """创建测试报告"""
    print("\n📊 生成测试报告...")
    
    deps_ok, missing = check_dependencies()
    
    report_content = f"""
# 地图测试报告

## 测试环境
- **测试时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **Python版本**: {sys.version}
- **Python路径**: {sys.executable}
- **工作目录**: {os.getcwd()}

## 依赖检查结果
- **状态**: {'✅ 通过' if deps_ok else '❌ 失败'}
- **缺失包**: {', '.join(missing) if missing else '无'}

## 测试结果
1. **基础HTML地图**: ✅ 已生成
2. **Python环境**: ✅ 正常
3. **文件写入**: ✅ 正常

## 建议
"""
    
    if missing:
        report_content += f"""
### 安装缺失的依赖
```bash
pip install {' '.join(missing)}
```

### 或者使用requirements.txt
```bash
pip install -r requirements.txt
```
"""
    else:
        report_content += """
### 运行完整测试
```bash
python simple_map_test.py
```
"""
    
    report_content += """
## 文件说明
- `basic_html_map_test.html` - 基础HTML地图，可直接在浏览器中打开
- `test_report.md` - 本测试报告

## 故障排除
1. 如果地图无法显示，请检查网络连接
2. 如果依赖安装失败，请尝试使用不同的Python环境
3. 确保有足够的磁盘空间和文件写入权限
"""
    
    filename = "test_report.md"
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report_content)
        print(f"✅ 测试报告已保存为: {filename}")
        return filename
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 地图测试脚本 - 基础版本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查依赖
    deps_ok, missing = check_dependencies()
    
    # 创建基础HTML地图
    map_file = create_basic_html_map()
    
    # 生成测试报告
    report_file = create_test_report()
    
    print("\n🎉 测试完成！")
    print("📁 生成的文件:")
    
    files_created = []
    if map_file and os.path.exists(map_file):
        files_created.append(map_file)
        print(f"   ✅ {map_file}")
    
    if report_file and os.path.exists(report_file):
        files_created.append(report_file)
        print(f"   ✅ {report_file}")
    
    if map_file:
        print(f"\n💡 提示: 双击 {map_file} 在浏览器中查看地图")
        
        # 询问是否打开浏览器
        try:
            choice = input("\n是否在浏览器中打开地图? (y/n): ").lower()
            if choice == 'y':
                webbrowser.open(map_file)
                print("🌐 已在浏览器中打开地图")
        except:
            pass
    
    if missing:
        print(f"\n⚠️  要运行完整的地图测试，请先安装: {' '.join(missing)}")
    else:
        print("\n✅ 环境准备就绪，可以运行完整的地图测试！")

if __name__ == "__main__":
    main()
