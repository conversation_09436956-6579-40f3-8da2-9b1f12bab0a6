@echo off
echo ================================================
echo     空气污染数据分析系统 - 一键启动脚本
echo ================================================
echo.
echo 正在启动所有服务，请稍候...
cd /d "C:\Users\<USER>\Desktop\8581-基于机器学习的空气污染数据分析系统\Algorithm"

echo [1/3] 启动地理信息分析服务 (端口8502)...
start "地理信息分析服务" C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m streamlit run geo_visualization.py --server.port 8502

timeout /t 3 /nobreak >nul

echo [2/3] 启动数据可视化分析服务 (端口8501)...
start "数据可视化分析服务" C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m streamlit run visual_analysis.py --server.port 8501

timeout /t 3 /nobreak >nul

echo [3/3] 启动机器学习预测服务 (端口8503)...
start "机器学习预测服务" C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m streamlit run streamlit_ui.py --server.port 8503

echo.
echo ================================================
echo              所有服务启动完成！
echo ================================================
echo.
echo 请在浏览器中访问以下地址：
echo.
echo 🌍 地理信息分析:     http://localhost:8502
echo 📊 数据可视化分析:   http://localhost:8501  
echo 🤖 机器学习预测:     http://localhost:8503
echo.
echo 注意：服务启动需要几秒钟时间，请稍候再访问
echo.
echo 按任意键退出...
pause >nul
