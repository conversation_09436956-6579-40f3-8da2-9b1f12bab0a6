
import { RouteRecordRaw } from 'vue-router';
export const dynamicRoutes: Array<RouteRecordRaw> = [
{
    path: '/',
    name: '/',
    component: () => import('/@/layout/index.vue'),
    meta: {
    isKeepAlive: true,
    },
    children: [],
},
{
    path: '/personal',
    name: 'personal',
    component: () => import('/@/views/system/personal/index.vue'),
    meta: {
    title: 'message.router.personal',
        isLink: '',
        isHide: false,
        isKeepAlive: true,
        isAffix: false,
        isIframe: false,
        icon: 'iconfont icon-gerenzhongxin',
    },
}
];

export const notFoundAndNoPower = [
];

export const staticRoutes: Array<RouteRecordRaw> = [
{
    path: '/',
    redirect: '/login',
},
{
    path: '/login',
    name: 'login',
    component: () => import('/@/views/front/login/login.vue'),
    meta: {
    title: '登录',
    }
},
{
    path: '/main',
    name: 'main',
    component: () => import('/@/views/front/main/main.vue'),
    meta: {
    title: '主页',
    },
    children: [

        {
        	path: '/main',
        	redirect: '/main-Streamlit',
        },
        {
            path: '/main-Streamlit',
            name: 'main-Streamlit',
            component: () => import('/@/views/front/main/main_Streamlit/index.vue'),
            meta: {
                title: '空气污染预测',
            }
        },

        {
            path: '/main-Streamlit1',
            name: 'main-Streamlit1',
            component: () => import('/@/views/front/main/main_Streamlit1/index.vue'),
            meta: {
                title: '空气污染数据可视化',
            }
        },

    
         {
            path: '/main-ai',
            name: 'main-ai',
            component: () => import('/@/views/front/main/ai/index.vue'),
            meta: {
            title: 'ai问答',
            }
        },

         {
            path: '/main-feedback',
            name: 'main-feedback',
            component: () => import('/@/views/front/main/feedback/index.vue'),
            meta: {
            title: '系统反馈',
            }
        },

    ],
},
{
    path: '/register',
    name: 'register',
    component: () => import('/@/views/front/register/register.vue'),
    meta: {
    title: '注册',
    }
},
{
    path: '/chart',
    name: 'chart',
    component: () => import('/@/views/chart/index.vue'),
    meta: {
    title: '图表',
        isReception:true,
    }
},
];
