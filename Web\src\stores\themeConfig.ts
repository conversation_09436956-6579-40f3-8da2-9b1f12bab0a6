
import { defineStore } from 'pinia';
export const useThemeConfig = defineStore('themeConfig', {
state: (): ThemeConfigState => ({

        // 是否显示后台配置按钮，使用时更改为false/true
        // "isHomeSetting": false, 
            themeConfig:
                {"isHomeSetting":false,"isDrawer":false,"primary":"#002DD0","isIsDark":false,"topBar":"#FFFFFF","topBarColor":"#000000","isTopBarColorGradual":false,"menuBar":"#626484","menuBarColor":"#030303","menuBarActiveColor":"rgba(0, 0, 0, 0.2)","isMenuBarColorGradual":true,"columnsMenuBar":"#334054","columnsMenuBarColor":"#e6e6e6","isColumnsMenuBarColorGradual":false,"isColumnsMenuHoverPreload":false,"isCollapse":false,"isUniqueOpened":true,"isFixedHeader":true,"isFixedHeaderChange":false,"isClassicSplitMenu":false,"isLockScreen":false,"lockScreenTime":30,"isShowLogo":true,"isShowLogoChange":false,"isBreadcrumb":false,"isTagsview":true,"isBreadcrumbIcon":true,"isTagsviewIcon":true,"isCacheTagsView":true,"isSortableTagsView":true,"isShareTagsView":true,"isFooter":true,"isGrayscale":false,"isInvert":false,"isWartermark":false,"wartermarkText":"","tagsStyle":"tags-style-four","animation":"opacitys","columnsAsideStyle":"columns-round","columnsAsideLayout":"columns-vertical","layout":"classic","isRequestRoutes":true,"globalTitle":"基于机器学习的空气污染数据分析系统","globalViceTitle":"基于机器学习的空气污染数据分析系统","globalViceTitleMsg":"基于机器学习的空气污染数据分析系统","globalI18n":"zh-cn","globalComponentSize":"default"}
            
}),
actions: {
setThemeConfig(data: ThemeConfigState) {
    this.themeConfig =
        data.themeConfig;
        },
        },
});