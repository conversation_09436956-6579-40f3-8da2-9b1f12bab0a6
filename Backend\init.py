from datetime import datetime
import csv
import subprocess
import sqlite3
def initialize_data():
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()

    # 初始化 Role 表默认数据
    cursor.executemany(
        'INSERT INTO Role (role_name) VALUES (?)',
        [
            ('用户',),
        ]
    )

    # 初始化 Profile 表默认数据
    cursor.executemany(
        'INSERT INTO Profile (username, name, gender, phone, email, role_id, dept, avatar, password) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
            ('admin', '小王', '男', '13333333333', '<EMAIL>', None, '学校', 'avatars/default.png', 'pbkdf2_sha256$720000$QjNMoyOKbcqrdfPk6ltxIZ$paQpZQieBWcUvXvtJa08N/8uUdwYXVGPo/NOfJhvTm8=',),
            ('user', '王00', '男', '10000000000', '<EMAIL>', 1, '学校', 'avatars/default.png', 'pbkdf2_sha256$720000$QjNMoyOKbcqrdfPk6ltxIZ$paQpZQieBWcUvXvtJa08N/8uUdwYXVGPo/NOfJhvTm8=',),
        ]
    )

    conn.commit()
    conn.close()

    print('初始化数据完成!')



def initialize_django_app():
    # 执行迁移
    subprocess.run(['python', 'manage.py', 'makemigrations', 'App'])
    subprocess.run(['python', 'manage.py', 'migrate'])

    # 初始化数据
    initialize_data()

    # 启动服务器
    subprocess.run(['python', 'manage.py', 'runserver'])

if __name__ == '__main__':
    initialize_django_app()