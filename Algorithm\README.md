# 基于机器学习的空气污染数据分析系统

## 项目概述

本项目旨在设计和实现一个基于机器学习的空气污染数据分析系统。系统通过分析空气污染数据，建立预测模型，为空气质量管理和污染控制提供决策支持。

## 数据集说明

本项目使用模拟生成的空气污染数据集，包含10,000条记录，具有以下特点：

### 污染物指标
- PM2.5：细颗粒物
- PM10：可吸入颗粒物
- SO2：二氧化硫
- NO2：二氧化氮
- CO：一氧化碳
- O3：臭氧

### 气象因素
- 温度
- 湿度
- 风速
- 降水量
- 大气压力

### 其他特征
- 时间特征：日期、小时、星期几、月份、季节、是否周末
- 地理区域：工业区、城市、郊区、农村
- 人类活动：工业活动指数、交通流量

### 数据关联性

数据集中的各特征之间存在合理的关联性，例如：
- 季节对污染物浓度的影响（如冬季PM2.5浓度较高）
- 工作日高峰时段交通流量增加导致相关污染物浓度上升
- 风速和降水对污染物的稀释和清除作用
- 温度对臭氧形成的促进作用
- 不同区域类型的污染物特征差异

## 文件说明

- `generate_air_pollution_dataset.py`：生成模拟空气污染数据集的脚本
- `air_pollution_dataset.csv`：生成的数据集文件
- 可视化图表：
  - `correlation_heatmap.png`：特征相关性热图
  - `pollutants_trend.png`：污染物随时间变化趋势
  - `area_pollutants_distribution.png`：不同区域的污染物分布
  - `season_pollutants_distribution.png`：不同季节的污染物分布

## 使用方法

### 生成数据集

```bash
python generate_air_pollution_dataset.py
```

执行上述命令将生成数据集文件和相关可视化图表。

### 数据分析与建模

后续将添加数据分析和机器学习建模脚本，用于：
- 数据预处理和特征工程
- 探索性数据分析
- 预测模型训练与评估
- 模型解释与可视化

## 项目特点

1. **多特征关联**：模拟数据集考虑了多种因素之间的复杂关系
2. **时空变化**：包含时间和空间维度的变化特征
3. **加权分布**：使用加权方法模拟真实世界中的数据分布特性
4. **适合机器学习**：数据集设计适合进行各类机器学习分析和建模