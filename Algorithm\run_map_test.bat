@echo off
chcp 65001 >nul
echo 🗺️ 地图测试脚本启动器
echo ================================
echo.
echo 请选择要运行的测试：
echo.
echo 1. 基础地图测试 (推荐，无需额外依赖)
echo 2. 完整地图测试 (需要安装 plotly, pandas, numpy)
echo 3. 安装依赖包
echo 4. 查看帮助
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto basic_test
if "%choice%"=="2" goto full_test
if "%choice%"=="3" goto install_deps
if "%choice%"=="4" goto help
if "%choice%"=="5" goto exit
goto invalid

:basic_test
echo.
echo 🚀 运行基础地图测试...
python simple_map_test_basic.py
goto end

:full_test
echo.
echo 🚀 运行完整地图测试...
python simple_map_test.py
goto end

:install_deps
echo.
echo 📦 安装依赖包...
pip install plotly pandas numpy
echo.
echo ✅ 依赖安装完成！
pause
goto start

:help
echo.
echo 📖 帮助信息
echo ============
echo.
echo 基础测试：
echo - 无需安装额外依赖
echo - 生成 HTML 地图文件
echo - 包含环境检查
echo.
echo 完整测试：
echo - 需要安装 plotly, pandas, numpy
echo - 生成多种类型的地图
echo - 包含性能测试
echo.
echo 生成的文件：
echo - basic_html_map_test.html (基础地图)
echo - test_report.md (测试报告)
echo - 其他 HTML 地图文件 (完整测试)
echo.
pause
goto start

:invalid
echo.
echo ❌ 无效选择，请重新输入
pause
goto start

:start
cls
goto :eof

:end
echo.
echo 🎉 测试完成！
echo 💡 提示：双击生成的 HTML 文件在浏览器中查看地图
pause

:exit
echo.
echo 👋 再见！
