import streamlit as st
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    st.set_page_config(
        page_title="空气污染数据分析系统",
        page_icon="🌍",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 自定义CSS样式
    st.markdown("""
    <style>
    .main {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 20px;
        border-radius: 15px;
        color: #000;
    }
    
    .stApp {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .title {
        color: #2c3e50;
        font-size: 42px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .subtitle {
        color: #34495e;
        font-size: 20px;
        text-align: center;
        margin-bottom: 40px;
        font-weight: 300;
    }
    
    .nav-button {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        border: none;
        padding: 20px 30px;
        border-radius: 15px;
        font-size: 16px;
        font-weight: bold;
        width: 100%;
        margin: 10px 0;
        box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        transition: all 0.3s ease;
        text-align: center;
        cursor: pointer;
    }
    
    .nav-button:hover {
        background: linear-gradient(45deg, #2980b9, #3498db);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(52, 152, 219, 0.6);
    }
    
    .feature-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        margin: 15px 0;
        text-align: center;
        transition: transform 0.3s ease;
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
    }
    
    .feature-icon {
        font-size: 48px;
        margin-bottom: 15px;
    }
    
    .feature-title {
        font-size: 20px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .feature-desc {
        color: #7f8c8d;
        font-size: 14px;
        line-height: 1.6;
    }
    
    .sidebar-nav {
        background: rgba(255,255,255,0.1);
        padding: 15px;
        border-radius: 10px;
        margin: 10px 0;
    }
    
    /* 确保所有文字都是黑色 */
    .stSelectbox label, .stRadio label {
        color: #000000 !important;
        font-weight: 600;
    }
    
    /* 侧边栏样式 */
    .css-1d391kg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .css-1d391kg .stSelectbox label, .css-1d391kg .stRadio label {
        color: white !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 侧边栏导航
    st.sidebar.markdown("## 🧭 系统导航")
    
    page = st.sidebar.selectbox(
        "选择功能模块",
        [
            "🏠 系统首页",
            "🔮 PM2.5浓度预测",
            "📊 数据可视化分析", 
            "🗺️ 地理信息分析",
            "📈 模型性能评估"
        ]
    )
    
    # 根据选择显示不同页面
    if page == "🏠 系统首页":
        show_homepage()
    elif page == "🔮 PM2.5浓度预测":
        show_prediction_page()
    elif page == "📊 数据可视化分析":
        show_visualization_page()
    elif page == "🗺️ 地理信息分析":
        show_geo_analysis_page()
    elif page == "📈 模型性能评估":
        show_model_evaluation_page()

def show_homepage():
    """显示系统首页"""
    st.markdown('<div class="title">🌍 空气污染数据分析系统</div>', unsafe_allow_html=True)
    st.markdown('<div class="subtitle">基于机器学习的智能环境监测与预测平台</div>', unsafe_allow_html=True)
    
    # 系统功能介绍
    st.markdown("## 🚀 系统功能")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">🔮</div>
            <div class="feature-title">PM2.5浓度预测</div>
            <div class="feature-desc">
                基于机器学习算法，输入环境参数预测PM2.5浓度，
                支持多种污染物指标，提供准确的空气质量预测。
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">🗺️</div>
            <div class="feature-title">地理信息分析</div>
            <div class="feature-desc">
                提供污染源和监测站的地理位置可视化，
                支持空间分析和热力图展示，直观了解污染分布。
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">📊</div>
            <div class="feature-title">数据可视化分析</div>
            <div class="feature-desc">
                多维度数据可视化展示，包括时间趋势、区域分布、
                污染物相关性分析和季节对比等功能。
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">📈</div>
            <div class="feature-title">模型性能评估</div>
            <div class="feature-desc">
                提供详细的模型性能指标，包括准确率、特征重要性、
                SHAP分析等，确保预测结果的可靠性。
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    # 系统特点
    st.markdown("## ✨ 系统特点")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        **🎯 高精度预测**
        - 使用先进的机器学习算法
        - 多特征融合建模
        - 实时数据处理
        """)
    
    with col2:
        st.markdown("""
        **🎨 直观可视化**
        - 交互式图表展示
        - 地理信息可视化
        - 多维度数据分析
        """)
    
    with col3:
        st.markdown("""
        **🔧 易于使用**
        - 友好的用户界面
        - 响应式设计
        - 实时结果反馈
        """)
    
    # 使用说明
    st.markdown("## 📖 使用说明")
    st.markdown("""
    1. **选择功能模块**：使用左侧导航栏选择需要的功能
    2. **输入参数**：根据页面提示输入相关参数
    3. **查看结果**：系统将实时显示分析结果和可视化图表
    4. **交互操作**：可以通过图表交互获取更详细的信息
    """)

def show_prediction_page():
    """显示预测页面"""
    try:
        from streamlit_ui import main as prediction_main
        prediction_main()
    except ImportError as e:
        st.error(f"无法加载预测模块: {e}")
        st.info("请确保所有依赖包已正确安装")

def show_visualization_page():
    """显示可视化分析页面"""
    try:
        from visual_analysis import main as visual_main
        visual_main()
    except ImportError as e:
        st.error(f"无法加载可视化模块: {e}")
        st.info("请确保所有依赖包已正确安装")

def show_geo_analysis_page():
    """显示地理信息分析页面"""
    try:
        from geo_visualization import main as geo_main
        geo_main()
    except ImportError as e:
        st.error(f"无法加载地理信息模块: {e}")
        st.info("请安装地理信息相关依赖包：pip install folium streamlit-folium geopandas")

def show_model_evaluation_page():
    """显示模型评估页面"""
    st.markdown('<div class="title">📈 模型性能评估</div>', unsafe_allow_html=True)
    st.markdown('<div class="subtitle">机器学习模型详细性能分析</div>', unsafe_allow_html=True)
    
    # 这里可以添加模型评估的具体实现
    st.info("模型评估功能正在开发中，敬请期待！")
    
    # 显示一些示例性能指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("模型准确率", "92.5%", "2.1%")
    with col2:
        st.metric("均方误差", "15.2", "-1.8")
    with col3:
        st.metric("R²得分", "0.89", "0.05")
    with col4:
        st.metric("训练时间", "3.2s", "-0.5s")

if __name__ == "__main__":
    main()
