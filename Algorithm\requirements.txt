# Data processing
numpy>=1.24.0,<2.0.0
pandas>=2.0.0,<3.0.0
scipy>=1.10.0,<2.0.0

# Visualization
matplotlib>=3.7.0,<4.0.0
seaborn>=0.12.0,<1.0.0
plotly>=5.15.0,<6.0.0
pyecharts>=2.0.0,<3.0.0

# Web interface
streamlit>=1.25.0,<2.0.0
streamlit-echarts>=0.4.0,<1.0.0

# Geographic visualization
folium>=0.14.0,<1.0.0
streamlit-folium>=0.13.0,<1.0.0
geopandas>=0.13.0,<1.0.0
shapely>=2.0.0,<3.0.0

# Machine learning
scikit-learn>=1.2.0,<2.0.0
xgboost>=1.7.0,<2.0.0
shap>=0.41.0,<1.0.0

# Other tools
joblib>=1.3.0,<2.0.0