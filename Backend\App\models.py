
import os
from uuid import uuid4
from django.db import models


class Role(models.Model):
    role_name = models.CharField(max_length=255, verbose_name='角色名称', null=True, blank=True, default='')
    description = models.TextField(verbose_name='角色描述', null=True, blank=True, default='')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间', null=True, blank=True)

    class Meta:
        verbose_name = '角色'
        verbose_name_plural = '角色'
        db_table = 'Role'


class Profile(models.Model):
    username = models.CharField(max_length=255, verbose_name='用户名', null=True, blank=True, default='', unique=True)
    password = models.CharField(max_length=255, verbose_name='密码', null=True, blank=True, default='')
    name = models.CharField(max_length=255, verbose_name='姓名', null=True, blank=True, default='')
    gender = models.CharField(max_length=255, verbose_name='性别', null=True, blank=True, default='')
    avatar = models.ImageField(upload_to='avatars/', verbose_name='头像', null=True, blank=True, default='avatars/default.png')
    phone = models.CharField(max_length=255, verbose_name='手机号', null=True, blank=True, default='')
    email = models.CharField(max_length=255, verbose_name='邮箱', null=True, blank=True, default='')
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, related_name='profiles', verbose_name='角色', null=True, blank=True, default=1)
    dept = models.CharField(max_length=255, verbose_name='所属部门', null=True, blank=True, default='')
    notes = models.TextField(verbose_name='备注', null=True, blank=True, default='')

    class Meta:
        verbose_name = '用户信息'
        verbose_name_plural = '用户信息'
        db_table = 'Profile'



class Swiper(models.Model):
    title = models.CharField(max_length=255, verbose_name='标题', null=True, blank=True, default='')
    image = models.ImageField(upload_to='swiper/images/', verbose_name='图片', null=True, blank=True)
    location = models.CharField(max_length=255, verbose_name='标题位置', null=True, blank=True, default='')

    class Meta:
        verbose_name = '轮播图'
        verbose_name_plural = '轮播图'
        db_table = 'Swiper'



class Feedback(models.Model):
    user = models.ForeignKey(Profile, on_delete=models.SET_NULL, related_name='feedbacks', verbose_name='用户', null=True, blank=True)
    subject = models.CharField(max_length=255, verbose_name='反馈主题', null=True, blank=True, default='')
    message = models.TextField(verbose_name='反馈信息', null=True, blank=True, default='')
    resolved = models.CharField(max_length=255, verbose_name='是否已解决', null=True, blank=True, default='')
    image = models.ImageField(upload_to='feedback/images/', verbose_name='配图', null=True, blank=True)
    video = models.FileField(upload_to='feedback/videos/', verbose_name='视频', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='反馈时间', null=True, blank=True)

    class Meta:
        verbose_name = '系统反馈'
        verbose_name_plural = '系统反馈'
        db_table = 'Feedback'


