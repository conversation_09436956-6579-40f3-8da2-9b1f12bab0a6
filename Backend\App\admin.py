from django.contrib import admin
from .models import *

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = [
        'role_name',
        'description',
        'created_at',
    ]

@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = [
        'username',
        'password',
        'name',
        'gender',
        'avatar',
        'phone',
        'email',
        'role',
        'dept',
        'notes',
    ]


@admin.register(Swiper)
class SwiperAdmin(admin.ModelAdmin):
    list_display = [
        'title',
        'image',
        'location',
    ]

@admin.register(Feedback)
class FeedbackAdmin(admin.ModelAdmin):
    list_display = [
        'user',
        'subject',
        'message',
        'resolved',
        'image',
        'video',
        'created_at',
    ]
