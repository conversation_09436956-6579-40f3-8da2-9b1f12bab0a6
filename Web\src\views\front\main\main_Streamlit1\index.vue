
<template>
  <div class="visualization-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">📊 空气污染数据可视化分析</h1>
      <p class="page-subtitle">多维度数据分析与地理信息可视化</p>
    </div>

    <!-- 分析模块选择 -->
    <div class="module-selector">
      <div class="selector-card">
        <h3 class="selector-title">🎛️ 选择分析模块</h3>
        <div class="module-buttons">
          <button
            v-for="module in analysisModules"
            :key="module.key"
            @click="selectModule(module.key)"
            :class="['module-btn', { active: selectedModule === module.key }]"
          >
            <span class="module-icon">{{ module.icon }}</span>
            <span class="module-name">{{ module.name }}</span>
            <span class="module-desc">{{ module.description }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 模块内容区域 -->
    <div class="content-area">
      <!-- 数据可视化模块 -->
      <div v-if="selectedModule === 'visualization'" class="module-content">
        <div class="module-header">
          <h2>📈 数据可视化分析</h2>
          <p>时间趋势、区域分布、污染物相关性分析</p>
        </div>
        <div class="iframe-container">
          <iframe
            src="http://127.0.0.1:8501"
            class="analysis-iframe"
            frameborder="0">
          </iframe>
        </div>
      </div>

      <!-- 地理信息分析模块 -->
      <div v-if="selectedModule === 'geographic'" class="module-content">
        <div class="module-header">
          <h2>🗺️ 地理信息分析</h2>
          <p>污染源与监测站空间分布可视化</p>
        </div>

        <!-- 地理分析控制面板 -->
        <div class="geo-controls">
          <div class="control-group">
            <label>分析类型:</label>
            <select v-model="geoAnalysisType" class="control-select">
              <option value="map">交互式地图</option>
              <option value="heatmap">污染物热力图</option>
              <option value="statistics">空间统计</option>
              <option value="sources">污染源分析</option>
            </select>
          </div>

          <div class="control-group">
            <label>污染物:</label>
            <select v-model="selectedPollutant" class="control-select">
              <option value="PM2.5">PM2.5</option>
              <option value="PM10">PM10</option>
              <option value="SO2">SO2</option>
              <option value="NO2">NO2</option>
              <option value="CO">CO</option>
              <option value="O3">O3</option>
            </select>
          </div>
        </div>

        <div class="iframe-container">
          <iframe
            :src="geoMapUrl"
            class="analysis-iframe"
            frameborder="0">
          </iframe>
        </div>
      </div>

      <!-- 综合分析模块 -->
      <div v-if="selectedModule === 'comprehensive'" class="module-content">
        <div class="module-header">
          <h2>🔍 综合分析</h2>
          <p>多维度综合数据分析与对比</p>
        </div>

        <!-- 综合分析面板 -->
        <div class="comprehensive-panel">
          <div class="analysis-grid">
            <!-- 数据概览卡片 -->
            <div class="overview-card">
              <h4>📊 数据概览</h4>
              <div class="metrics-row">
                <div class="metric-item">
                  <span class="metric-value">{{ stationsCount }}</span>
                  <span class="metric-label">监测站</span>
                </div>
                <div class="metric-item">
                  <span class="metric-value">{{ sourcesCount }}</span>
                  <span class="metric-label">污染源</span>
                </div>
                <div class="metric-item">
                  <span class="metric-value">{{ avgPM25 }}</span>
                  <span class="metric-label">平均PM2.5</span>
                </div>
                <div class="metric-item">
                  <span class="metric-value">{{ goodRatio }}%</span>
                  <span class="metric-label">优良率</span>
                </div>
              </div>
            </div>

            <!-- 快速分析工具 -->
            <div class="tools-card">
              <h4>🛠️ 快速分析</h4>
              <div class="tool-buttons">
                <button @click="openVisualization" class="tool-btn">
                  📈 打开数据可视化
                </button>
                <button @click="openGeographic" class="tool-btn">
                  🗺️ 打开地理分析
                </button>
                <button @click="refreshData" class="tool-btn">
                  🔄 刷新数据
                </button>
              </div>
            </div>
          </div>

          <!-- 双屏显示 -->
          <div class="dual-screen">
            <div class="screen-left">
              <h4>📈 数据趋势</h4>
              <iframe
                src="http://127.0.0.1:8501"
                class="mini-iframe"
                frameborder="0">
              </iframe>
            </div>
            <div class="screen-right">
              <h4>🗺️ 地理分布</h4>
              <iframe
                :src="geoMapUrl"
                class="mini-iframe"
                frameborder="0">
              </iframe>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 页面底部信息 -->
    <div class="page-footer">
      <div class="footer-info">
        <p><strong>💡 使用提示：</strong></p>
        <ul>
          <li>选择不同的分析模块查看对应的可视化内容</li>
          <li>数据可视化模块提供时间趋势和相关性分析</li>
          <li>地理信息模块提供空间分布和污染源分析</li>
          <li>综合分析模块提供多维度对比分析</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const selectedModule = ref('visualization')
const geoAnalysisType = ref('map')
const selectedPollutant = ref('PM2.5')

// 基础数据
const stationsCount = ref(16)
const sourcesCount = ref(25)
const avgPM25 = ref(76.6)
const goodRatio = ref(50.0)

// 分析模块配置
const analysisModules = ref([
  {
    key: 'visualization',
    name: '数据可视化',
    icon: '📈',
    description: '时间趋势、相关性分析'
  },
  {
    key: 'geographic',
    name: '地理信息',
    icon: '🗺️',
    description: '空间分布、污染源分析'
  },
  {
    key: 'comprehensive',
    name: '综合分析',
    icon: '🔍',
    description: '多维度对比分析'
  }
])

// 计算属性
const geoMapUrl = computed(() => {
  return `http://127.0.0.1:8502?analysis=${geoAnalysisType.value}&pollutant=${selectedPollutant.value}`
})

// 方法
const selectModule = (moduleKey: string) => {
  selectedModule.value = moduleKey
  console.log('切换到模块:', moduleKey)
}

const openVisualization = () => {
  selectedModule.value = 'visualization'
}

const openGeographic = () => {
  selectedModule.value = 'geographic'
}

const refreshData = () => {
  console.log('刷新数据...')
  stationsCount.value = Math.floor(Math.random() * 20) + 10
  sourcesCount.value = Math.floor(Math.random() * 30) + 20
  avgPM25.value = Math.floor(Math.random() * 100) + 20
  goodRatio.value = Math.floor(Math.random() * 80) + 20
}

onMounted(() => {
  console.log('可视化分析页面已加载')
})
</script>

<style scoped>
.visualization-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 20px;
  color: #000;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  color: #2c3e50;
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.page-subtitle {
  color: #34495e;
  font-size: 18px;
  font-weight: 300;
  margin-bottom: 0;
}

/* 模块选择器 */
.module-selector {
  margin-bottom: 30px;
}

.selector-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
}

.selector-title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #3498db;
}

.module-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.module-btn {
  background: white;
  border: 2px solid #e0e6ed;
  border-radius: 15px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.module-btn:hover {
  border-color: #3498db;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.module-btn.active {
  border-color: #3498db;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.module-icon {
  font-size: 32px;
  margin-bottom: 5px;
}

.module-name {
  font-size: 18px;
  font-weight: bold;
  color: #000;
}

.module-btn.active .module-name {
  color: white;
}

.module-desc {
  font-size: 14px;
  color: #7f8c8d;
}

.module-btn.active .module-desc {
  color: rgba(255,255,255,0.9);
}

/* 内容区域 */
.content-area {
  margin-bottom: 30px;
}

.module-content {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  overflow: hidden;
}

.module-header {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  padding: 20px;
  text-align: center;
}

.module-header h2 {
  margin: 0 0 5px 0;
  font-size: 24px;
}

.module-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

/* 地理控制面板 */
.geo-controls {
  background: #f8f9fa;
  padding: 20px;
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group label {
  color: #000;
  font-weight: 600;
  font-size: 14px;
}

.control-select {
  padding: 8px 12px;
  border: 2px solid #e0e6ed;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  color: #2c3e50;
  min-width: 150px;
}

.control-select:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* iframe容器 */
.iframe-container {
  height: 700px;
  position: relative;
}

.analysis-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* 综合分析面板 */
.comprehensive-panel {
  padding: 20px;
}

.analysis-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.overview-card, .tools-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.overview-card h4, .tools-card h4 {
  color: #2c3e50;
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
}

.metrics-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.metric-item {
  text-align: center;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.metric-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 12px;
  color: #7f8c8d;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.tool-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.tool-btn {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.tool-btn:hover {
  background: linear-gradient(45deg, #2980b9, #3498db);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

/* 双屏显示 */
.dual-screen {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  height: 400px;
}

.screen-left, .screen-right {
  background: #f8f9fa;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.screen-left h4, .screen-right h4 {
  background: #3498db;
  color: white;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  font-weight: 600;
}

.mini-iframe {
  width: 100%;
  height: calc(100% - 44px);
  border: none;
}

/* 页面底部 */
.page-footer {
  margin-top: 30px;
}

.footer-info {
  background: #e8f4fd;
  border-left: 4px solid #3498db;
  padding: 20px;
  border-radius: 5px;
  color: #000;
}

.footer-info p {
  margin: 0 0 10px 0;
  font-weight: 600;
}

.footer-info ul {
  margin: 0;
  padding-left: 20px;
}

.footer-info li {
  margin-bottom: 5px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .module-buttons {
    grid-template-columns: 1fr;
  }

  .geo-controls {
    flex-direction: column;
    align-items: center;
  }

  .analysis-grid {
    grid-template-columns: 1fr;
  }

  .dual-screen {
    grid-template-columns: 1fr;
    height: auto;
  }

  .screen-left, .screen-right {
    height: 300px;
  }

  .metrics-row {
    grid-template-columns: 1fr;
  }

  .iframe-container {
    height: 500px;
  }
}
</style>
