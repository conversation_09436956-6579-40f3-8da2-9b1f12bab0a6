@echo off
echo ================================================
echo     重启地理信息分析服务（性能优化版本）
echo ================================================
echo.
echo 正在停止现有服务...

:: 尝试停止可能运行的streamlit进程
taskkill /f /im python.exe 2>nul
timeout /t 2 /nobreak >nul

echo 正在启动优化后的地理信息分析服务...
cd /d "C:\Users\<USER>\Desktop\8581-基于机器学习的空气污染数据分析系统\Algorithm"

echo.
echo 🚀 启动地理信息分析服务 (端口8502)...
echo 📍 访问地址: http://localhost:8502
echo.
echo ⚡ 新功能：
echo   - 性能优化：减少渲染对象数量
echo   - 轻量模式：适合低配置设备
echo   - 修复兼容性问题
echo.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m streamlit run geo_visualization.py --server.port 8502

echo.
echo 服务已停止
pause
