
<template>
  <div class="dashboard">
    <!-- 标题 -->
    <h1 class="title">数据可视化大屏</h1>

  </div>
</template>
<script setup>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';
import VChart from 'vue-echarts';
import { ref, onMounted } from 'vue'
import axios from 'axios'

// 注册 ECharts 模块
use([<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, TitleComponent, Tooltip<PERSON>omponent, LegendComponent, GridComponent]);
onMounted(() => {

})

</script>
<style scoped>
.dashboard{
  height: 100%;
  min-height: 100vh;
  overflow-y:scroll;

}
/* 你可以在这里添加一些样式 */
.title {
  text-align: center;
  font-size: xx-large;
}
.chart {
  height: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>