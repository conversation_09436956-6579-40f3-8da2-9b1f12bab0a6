import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from datetime import datetime, timedelta
import random
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
import shap
import joblib
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子以确保结果可重现
np.random.seed(42)
random.seed(42)

# === 数据生成部分 ===

# 定义样本数量
n_samples = 10000
start_date = datetime(2023, 8, 1)
date_range = [start_date + timedelta(hours=i) for i in range(n_samples)]

# 定义权重参数
season_weights = {
    'winter': {'PM2.5': 1.5, 'PM10': 1.3, 'SO2': 1.4, 'NO2': 1.3, 'CO': 1.4, 'O3': 0.7},
    'spring': {'PM2.5': 1.2, 'PM10': 1.4, 'SO2': 1.0, 'NO2': 1.0, 'CO': 0.9, 'O3': 1.2},
    'summer': {'PM2.5': 0.8, 'PM10': 0.9, 'SO2': 0.8, 'NO2': 0.9, 'CO': 0.7, 'O3': 1.5},
    'fall': {'PM2.5': 1.1, 'PM10': 1.1, 'SO2': 1.1, 'NO2': 1.1, 'CO': 1.0, 'O3': 1.0}
}

time_weights = {
    'weekday_peak': {'PM2.5': 1.3, 'PM10': 1.3, 'SO2': 1.2, 'NO2': 1.4, 'CO': 1.3, 'O3': 0.9},
    'weekday_normal': {'PM2.5': 1.0, 'PM10': 1.0, 'SO2': 1.0, 'NO2': 1.0, 'CO': 1.0, 'O3': 1.0},
    'weekend': {'PM2.5': 0.8, 'PM10': 0.8, 'SO2': 0.7, 'NO2': 0.7, 'CO': 0.8, 'O3': 1.1}
}

pollutant_params = {
    'PM2.5': {'base_mean': 35, 'base_std': 10, 'min': 5, 'max': 300},  # 减小随机性
    'PM10': {'base_mean': 70, 'base_std': 35, 'min': 10, 'max': 500},
    'SO2': {'base_mean': 15, 'base_std': 10, 'min': 2, 'max': 150},
    'NO2': {'base_mean': 40, 'base_std': 20, 'min': 5, 'max': 200},
    'CO': {'base_mean': 1.0, 'base_std': 0.5, 'min': 0.2, 'max': 10.0},
    'O3': {'base_mean': 60, 'base_std': 30, 'min': 10, 'max': 250}
}

weather_params = {
    'temperature': {'winter': (0, 10), 'spring': (10, 25), 'summer': (25, 40), 'fall': (10, 25)},
    'humidity': {'winter': (30, 60), 'spring': (40, 70), 'summer': (60, 90), 'fall': (40, 70)},
    'wind_speed': {'base_mean': 3.0, 'base_std': 1.5, 'min': 0, 'max': 15},
    'precipitation': {'probability': {'winter': 0.3, 'spring': 0.4, 'summer': 0.5, 'fall': 0.3}, 'scale': 5.0},
    'atmospheric_pressure': {'base_mean': 1013, 'base_std': 10, 'min': 980, 'max': 1040}
}

area_types = ['industrial', 'urban', 'suburban', 'rural']
area_weights = {
    'industrial': {'PM2.5': 1.4, 'PM10': 1.3, 'SO2': 1.5, 'NO2': 1.3, 'CO': 1.3, 'O3': 0.9},
    'urban': {'PM2.5': 1.2, 'PM10': 1.2, 'SO2': 1.1, 'NO2': 1.2, 'CO': 1.2, 'O3': 1.0},
    'suburban': {'PM2.5': 0.9, 'PM10': 0.9, 'SO2': 0.8, 'NO2': 0.9, 'CO': 0.9, 'O3': 1.1},
    'rural': {'PM2.5': 0.7, 'PM10': 0.7, 'SO2': 0.6, 'NO2': 0.6, 'CO': 0.7, 'O3': 1.2}
}

industry_activity_params = {'base_mean': 50, 'base_std': 20, 'min': 0, 'max': 100}
traffic_flow_params = {'base_mean': 60, 'base_std': 25, 'min': 10, 'max': 100}

def calculate_aqi(row):
    pm25 = row['PM2.5']
    if pm25 <= 12:
        return np.interp(pm25, [0, 12], [0, 50])
    elif pm25 <= 35.4:
        return np.interp(pm25, [12.1, 35.4], [51, 100])
    elif pm25 <= 55.4:
        return np.interp(pm25, [35.5, 55.4], [101, 150])
    elif pm25 <= 150.4:
        return np.interp(pm25, [55.5, 150.4], [151, 200])
    elif pm25 <= 250.4:
        return np.interp(pm25, [150.5, 250.4], [201, 300])
    else:
        return np.interp(min(pm25, 500), [250.5, 500], [301, 500])

def generate_data():
    data = {
        'timestamp': date_range,
        'area_type': np.random.choice(area_types, size=n_samples, p=[0.2, 0.4, 0.3, 0.1])
    }
    data['date'] = [ts.date() for ts in data['timestamp']]
    data['hour'] = [ts.hour for ts in data['timestamp']]
    data['day_of_week'] = [ts.weekday() for ts in data['timestamp']]
    data['is_weekend'] = [1 if day >= 5 else 0 for day in data['day_of_week']]
    data['month'] = [ts.month for ts in data['timestamp']]
    data['season'] = ['winter' if m in [12, 1, 2] else 'spring' if m in [3, 4, 5] else
                     'summer' if m in [6, 7, 8] else 'fall' for m in data['month']]
    data['time_type'] = ['weekday_peak' if (not is_weekend and (h in [7, 8, 9, 17, 18, 19])) else
                        'weekend' if is_weekend else 'weekday_normal'
                        for is_weekend, h in zip(data['is_weekend'], data['hour'])]

    # 气象数据
    data['temperature'] = [np.random.uniform(weather_params['temperature'][season][0],
                                            weather_params['temperature'][season][1])
                          for season in data['season']]
    data['humidity'] = [np.random.uniform(weather_params['humidity'][season][0],
                                         weather_params['humidity'][season][1])
                       for season in data['season']]
    data['wind_speed'] = np.clip(np.random.normal(weather_params['wind_speed']['base_mean'],
                                                 weather_params['wind_speed']['base_std'], n_samples),
                                weather_params['wind_speed']['min'], weather_params['wind_speed']['max'])
    data['precipitation'] = [np.random.exponential(weather_params['precipitation']['scale'])
                            if np.random.random() < weather_params['precipitation']['probability'][season]
                            else 0 for season in data['season']]
    data['atmospheric_pressure'] = np.clip(np.random.normal(weather_params['atmospheric_pressure']['base_mean'],
                                                           weather_params['atmospheric_pressure']['base_std'], n_samples),
                                          weather_params['atmospheric_pressure']['min'],
                                          weather_params['atmospheric_pressure']['max'])

    # 工业活动和交通流量
    data['industry_activity'] = np.clip(np.random.normal(industry_activity_params['base_mean'],
                                                        industry_activity_params['base_std'], n_samples),
                                       industry_activity_params['min'], industry_activity_params['max'])
    for i, area in enumerate(data['area_type']):
        if area == 'industrial': data['industry_activity'][i] *= 1.5
        elif area == 'urban': data['industry_activity'][i] *= 1.2
        elif area == 'suburban': data['industry_activity'][i] *= 0.8
        else: data['industry_activity'][i] *= 0.4

    data['traffic_flow'] = np.clip(np.random.normal(traffic_flow_params['base_mean'],
                                                   traffic_flow_params['base_std'], n_samples),
                                  traffic_flow_params['min'], traffic_flow_params['max'])
    for i, (time_type, area) in enumerate(zip(data['time_type'], data['area_type'])):
        multiplier = 1.0
        if time_type == 'weekday_peak': multiplier *= 1.5
        elif time_type == 'weekend': multiplier *= 0.8
        if area == 'urban': multiplier *= 1.4
        elif area == 'suburban': multiplier *= 1.1
        elif area == 'industrial': multiplier *= 0.9
        else: multiplier *= 0.5
        data['traffic_flow'][i] *= multiplier

    # 污染物数据
    for pollutant, params in pollutant_params.items():
        base_values = np.random.normal(params['base_mean'], params['base_std'], n_samples)
        if pollutant == 'PM2.5':
            weighted_values = base_values + 0.3 * data['traffic_flow'] + 0.4 * data['industry_activity']
        else:
            weighted_values = base_values.copy()

        for i in range(n_samples):
            season_weight = season_weights[data['season'][i]][pollutant]
            time_weight = time_weights[data['time_type'][i]][pollutant]
            area_weight = area_weights[data['area_type'][i]][pollutant]
            weighted_values[i] *= (season_weight + time_weight + area_weight) / 3
            wind_effect = max(0.5, 1.0 - 0.03 * data['wind_speed'][i])
            rain_effect = max(0.6, 1.0 - 0.05 * data['precipitation'][i]) if data['precipitation'][i] > 0 else 1.0
            humidity_effect = 1.0 + 0.003 * (data['humidity'][i] - 50) if pollutant in ['PM2.5', 'PM10'] else 1.0
            weighted_values[i] *= wind_effect * rain_effect * humidity_effect
            if pollutant == 'O3':
                temp_effect = 1.0 + 0.02 * (data['temperature'][i] - 25)
                humidity_effect = 1.0 - 0.015 * (data['humidity'][i] - 60)
                weighted_values[i] *= temp_effect * humidity_effect
        data[pollutant] = np.clip(weighted_values, params['min'], params['max'])

    df = pd.DataFrame(data)
    df['AQI'] = df.apply(calculate_aqi, axis=1)
    df['AQI_category'] = pd.cut(df['AQI'], bins=[0, 50, 100, 150, 200, 300, 500],
                               labels=['优', '良', '轻度污染', '中度污染', '重度污染', '严重污染'], right=False)

    print("\nPM2.5 分布:")
    print(df['PM2.5'].describe())
    plt.hist(df['PM2.5'], bins=50)
    plt.title('PM2.5 Distribution')
    plt.savefig('pm25_distribution.png', dpi=300)
    plt.close()

    df.to_csv('air_pollution_dataset.csv', index=False)
    print("\n数据集已保存为 air_pollution_dataset.csv")
    return df

# === 模型训练部分 ===

def load_data(file_path='air_pollution_dataset.csv'):
    df = pd.read_csv(file_path)
    print(f"数据加载完成，共 {df.shape[0]} 行，{df.shape[1]} 列")
    return df

def preprocess_data(df, feature_cols):
    df['date'] = pd.to_datetime(df['date'])
    numeric_features = [col for col in feature_cols if df[col].dtype in ['float64', 'int64']]
    categorical_features = [col for col in feature_cols if col in ['area_type', 'season', 'time_type', 'AQI_category']]

    print("Numeric features:", numeric_features)
    print("Categorical features:", categorical_features)

    numeric_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='median')),
        ('scaler', StandardScaler())
    ])
    categorical_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='most_frequent')),
        ('onehot', OneHotEncoder(handle_unknown='ignore'))
    ])
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', numeric_transformer, numeric_features),
            ('cat', categorical_transformer, categorical_features)
        ])
    return preprocessor, df

def feature_engineering(df):
    df['day_of_year'] = df['date'].dt.dayofyear
    df['week_of_year'] = df['date'].dt.isocalendar().week.astype(int)
    df['temp_humidity'] = df['temperature'] * df['humidity'] / 100
    df['wind_temp_ratio'] = df['wind_speed'] / (df['temperature'] + 1)
    df['industry_traffic'] = df['industry_activity'] * df['traffic_flow'] / 100
    df['pm_ratio'] = df['PM2.5'] / (df['PM10'] + 1)
    df['no2_so2_ratio'] = df['NO2'] / (df['SO2'] + 1)
    df['weather_index'] = (df['temperature'] - df['humidity'] / 10 + df['wind_speed'] * 3 - df['precipitation'] * 2) / 10
    print("特征工程完成，新增特征:")
    print("- 时间特征: day_of_year, week_of_year")
    print("- 交互特征: temp_humidity, wind_temp_ratio, industry_traffic")
    print("- 污染物比率: pm_ratio, no2_so2_ratio")
    print("- 气象综合指数: weather_index")
    return df

def train_models(X_train, X_test, y_train, y_test):
    models = {
        'Linear Regression': LinearRegression(),
        'Ridge Regression': Ridge(),
        'Random Forest': RandomForestRegressor(n_estimators=50, random_state=42),  # Reduced for speed
    }
    results = {}
    best_model = None
    best_score = float('-inf')

    for name, model in models.items():
        print(f"\n训练模型: {name}")
        model.fit(X_train, y_train)
        y_pred_train = model.predict(X_train)
        y_pred_test = model.predict(X_test)
        r2_train = r2_score(y_train, y_pred_train)
        r2_test = r2_score(y_test, y_pred_test)
        mse = mean_squared_error(y_test, y_pred_test)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test, y_pred_test)

        results[name] = {'model': model, 'mse': mse, 'rmse': rmse, 'mae': mae, 'r2': r2_test}
        print(f"  Train R²: {r2_train:.4f}")
        print(f"  Test R²: {r2_test:.4f}")
        print(f"  MSE: {mse:.4f}")
        print(f"  RMSE: {rmse:.4f}")
        print(f"  MAE: {mae:.4f}")

        if r2_test > best_score:
            best_score = r2_test
            best_model = model

    print(f"\n最佳模型: {[name for name, res in results.items() if res['model'] == best_model][0]}")
    print(f"Test R² 得分: {best_score:.4f}")
    return results, best_model

def tune_best_model(X_train, y_train, best_model_name, results):
    print(f"\n对最佳模型 {best_model_name} 进行超参数调优...")
    if best_model_name == 'Linear Regression':
        print("线性回归没有需要调优的超参数")
        return results[best_model_name]['model']
    elif best_model_name == 'Ridge Regression':
        param_grid = {'alpha': [0.001, 0.01, 0.1, 1.0, 10.0, 100.0]}
        base_model = Ridge()
    elif best_model_name == 'Lasso Regression':
        param_grid = {'alpha': [0.001, 0.01, 0.1, 1.0, 10.0], 'max_iter': [1000, 2000, 3000]}
        base_model = Lasso()
    elif best_model_name == 'Random Forest':
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [10, 20, None],
            'min_samples_split': [2, 5],
            'min_samples_leaf': [1, 2]
        }
        base_model = RandomForestRegressor(random_state=42)
    elif best_model_name == 'Gradient Boosting':
        param_grid = {
            'n_estimators': [100, 200, 300],
            'learning_rate': [0.01, 0.05, 0.1],
            'max_depth': [3, 5, 7],
            'min_samples_split': [2, 5]
        }
        base_model = GradientBoostingRegressor(random_state=42)
    elif best_model_name == 'XGBoost':
        param_grid = {
            'n_estimators': [100, 200, 300],
            'learning_rate': [0.01, 0.05, 0.1, 0.2],
            'max_depth': [3, 5, 7, 9],
            'subsample': [0.7, 0.8, 0.9, 1.0],
            'colsample_bytree': [0.7, 0.8, 0.9, 1.0]
        }
        base_model = xgb.XGBRegressor(random_state=42)

    grid_search = GridSearchCV(base_model, param_grid, cv=5, scoring='r2', n_jobs=-1)
    grid_search.fit(X_train, y_train)
    print(f"最佳参数: {grid_search.best_params_}")
    print(f"最佳交叉验证得分: {grid_search.best_score_:.4f}")
    return grid_search.best_estimator_

def explain_model(model, X_train, feature_names):
    print("\n生成模型解释...")
    if isinstance(model, (RandomForestRegressor, GradientBoostingRegressor, xgb.XGBRegressor)):
        importances = model.feature_importances_
        feature_importance = pd.DataFrame({'feature': feature_names, 'importance': importances})
        feature_importance = feature_importance.sort_values('importance', ascending=False)

        plt.figure(figsize=(12, 8))
        sns.barplot(x='importance', y='feature', data=feature_importance.head(15))
        plt.title('特征重要性', fontsize=16)
        plt.xlabel('重要性', fontsize=14)
        plt.ylabel('特征', fontsize=14)
        plt.tight_layout()
        plt.savefig('model_feature_importance.png', dpi=300)
        plt.close()

        print("特征重要性图已保存为 'model_feature_importance.png'")

        try:
            print("\n计算SHAP值...")
            explainer = shap.Explainer(model)
            shap_values = explainer(X_train)
            plt.figure(figsize=(12, 10))
            shap.summary_plot(shap_values, X_train, feature_names=feature_names, show=False)
            plt.title('SHAP值摘要图', fontsize=16)
            plt.tight_layout()
            plt.savefig('model_shap_summary.png', dpi=300)
            plt.close()
            print("SHAP值可视化已保存为 'model_shap_summary.png'")
        except Exception as e:
            print(f"SHAP值计算出错: {e}")
    elif isinstance(model, (LinearRegression, Ridge, Lasso)):
        coefficients = pd.DataFrame({'feature': feature_names, 'coefficient': model.coef_})
        coefficients = coefficients.sort_values('coefficient', ascending=False)
        plt.figure(figsize=(12, 10))
        sns.barplot(x='coefficient', y='feature', data=coefficients)
        plt.title('模型系数', fontsize=16)
        plt.xlabel('系数值', fontsize=14)
        plt.ylabel('特征', fontsize=14)
        plt.tight_layout()
        plt.savefig('model_coefficients.png', dpi=300)
        plt.close()
        print("模型系数图已保存为 'model_coefficients.png'")

def main():
    # 生成并加载数据
    df = generate_data()
    df = load_data()

    # 定义特征列
    exclude_cols = ['timestamp', 'date', 'PM2.5', 'AQI', 'AQI_category']
    feature_cols = [col for col in df.columns if col not in exclude_cols]

    # 数据预处理
    preprocessor, df = preprocess_data(df, feature_cols)

    # 特征工程
    df = feature_engineering(df)
    feature_cols = [col for col in df.columns if col not in exclude_cols]


    # 准备训练数据
    print("\n准备训练数据...")
    target = 'PM2.5'
    X_processed = preprocessor.fit_transform(df[feature_cols])
    y = df[target]
    feature_names = preprocessor.get_feature_names_out()

    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(X_processed, y, test_size=0.2, random_state=42)
    print(f"训练集大小: {X_train.shape[0]}")
    print(f"测试集大小: {X_test.shape[0]}")

    # 训练模型（跳过特征选择，使用所有特征）
    results, best_model = train_models(X_train, X_test, y_train, y_test)
    best_model_name = [name for name, res in results.items() if res['model'] == best_model][0]

    # 超参数调优
    tuned_model = tune_best_model(X_train, y_train, best_model_name, results)


    joblib.dump(preprocessor, 'preprocessor.pkl')
    # 保存模型
    joblib.dump(tuned_model, 'best_model.pkl')
    print("\n最佳模型已保存为 'best_model.pkl'")



    # 模型解释
    explain_model(tuned_model, X_train, feature_names)

if __name__ == "__main__":
    main()