#!/usr/bin/env python3
"""
地理信息数据分析系统启动脚本
"""

import subprocess
import sys
import os

def check_and_install_dependencies():
    """检查并安装必要的依赖包"""
    required_packages = [
        'streamlit',
        'pandas',
        'numpy',
        'plotly'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")

    if missing_packages:
        print(f"\n正在安装缺失的包: {', '.join(missing_packages)}")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                return False

    return True

def main():
    """主函数"""
    print("🌍 空气污染数据分析系统 - 地理信息模块")
    print("=" * 50)

    # 检查依赖
    print("正在检查依赖包...")
    if not check_and_install_dependencies():
        print("❌ 依赖包安装失败，请手动安装")
        return

    print("\n✅ 所有依赖包已准备就绪")
    print("🚀 正在启动地理信息分析系统...")

    # 启动Streamlit应用
    try:
        os.system("streamlit run geo_visualization.py --server.port 8502")
    except KeyboardInterrupt:
        print("\n👋 系统已关闭")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
