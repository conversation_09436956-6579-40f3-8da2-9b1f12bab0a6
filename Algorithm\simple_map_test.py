#!/usr/bin/env python3
"""
简单地图测试脚本
用于测试地图渲染功能是否正常工作
"""

import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
import webbrowser
import os
from datetime import datetime

def test_basic_map():
    """测试基础地图渲染"""
    print("🗺️ 测试基础地图渲染...")

    # 创建简单的散点地图
    fig = go.Figure(go.Scattermapbox(
        lat=[39.9042, 39.9142, 39.8942],
        lon=[116.4074, 116.4174, 116.3974],
        mode='markers',
        marker=dict(size=14, color='red'),
        text=['北京市中心', '测试点1', '测试点2'],
        hoverinfo='text'
    ))

    fig.update_layout(
        mapbox=dict(
            style="open-street-map",
            center=dict(lat=39.9042, lon=116.4074),
            zoom=10
        ),
        height=500,
        title="基础地图测试"
    )

    # 保存并打开
    filename = "basic_map_test.html"
    fig.write_html(filename)
    print(f"✅ 基础地图已保存为: {filename}")
    return fig

def test_choropleth_map():
    """测试区域填充地图"""
    print("🌍 测试区域填充地图...")

    # 使用内置的地理数据
    df = px.data.gapminder().query("year==2007")

    fig = px.choropleth(
        df,
        locations="iso_alpha",
        color="lifeExp",
        hover_name="country",
        color_continuous_scale=px.colors.sequential.Plasma,
        title="世界各国预期寿命分布图 (2007年)"
    )

    fig.update_layout(height=500)

    # 保存
    filename = "choropleth_map_test.html"
    fig.write_html(filename)
    print(f"✅ 区域填充地图已保存为: {filename}")
    return fig

def test_scatter_mapbox():
    """测试散点地图"""
    print("📍 测试散点地图...")

    # 生成北京周边的测试数据
    np.random.seed(42)
    n_points = 20

    # 北京市中心坐标
    center_lat, center_lon = 39.9042, 116.4074

    # 生成随机坐标
    lats = center_lat + np.random.normal(0, 0.1, n_points)
    lons = center_lon + np.random.normal(0, 0.1, n_points)

    # 生成模拟的空气质量数据
    pm25_values = np.random.uniform(20, 150, n_points)

    # 创建数据框
    df = pd.DataFrame({
        'latitude': lats,
        'longitude': lons,
        'PM2.5': pm25_values,
        'station_name': [f'监测站_{i+1}' for i in range(n_points)]
    })

    # 根据PM2.5值分配颜色
    def get_aqi_color(pm25):
        if pm25 <= 35:
            return 'green'
        elif pm25 <= 75:
            return 'yellow'
        elif pm25 <= 115:
            return 'orange'
        else:
            return 'red'

    df['color'] = df['PM2.5'].apply(get_aqi_color)

    fig = px.scatter_mapbox(
        df,
        lat='latitude',
        lon='longitude',
        color='PM2.5',
        size='PM2.5',
        hover_name='station_name',
        hover_data={'PM2.5': True, 'latitude': False, 'longitude': False},
        color_continuous_scale='RdYlGn_r',
        size_max=15,
        zoom=9,
        center={'lat': center_lat, 'lon': center_lon},
        mapbox_style='open-street-map',
        title='北京地区PM2.5监测站分布'
    )

    fig.update_layout(height=600)

    # 保存
    filename = "scatter_mapbox_test.html"
    fig.write_html(filename)
    print(f"✅ 散点地图已保存为: {filename}")
    return fig

def test_heatmap():
    """测试热力图"""
    print("🔥 测试热力图...")

    # 生成网格数据
    lat_range = np.linspace(39.8, 40.0, 20)
    lon_range = np.linspace(116.2, 116.6, 20)

    # 创建网格
    lat_grid, lon_grid = np.meshgrid(lat_range, lon_range)

    # 生成模拟的污染浓度数据（以北京市中心为高值区域）
    center_lat, center_lon = 39.9042, 116.4074
    pollution_data = 100 * np.exp(-((lat_grid - center_lat)**2 + (lon_grid - center_lon)**2) * 1000)

    fig = go.Figure(go.Densitymapbox(
        lat=lat_grid.flatten(),
        lon=lon_grid.flatten(),
        z=pollution_data.flatten(),
        radius=10,
        colorscale='Hot',
        showscale=True,
        hovertemplate='纬度: %{lat}<br>经度: %{lon}<br>污染浓度: %{z:.1f}<extra></extra>'
    ))

    fig.update_layout(
        mapbox=dict(
            style="open-street-map",
            center=dict(lat=center_lat, lon=center_lon),
            zoom=9
        ),
        height=600,
        title="污染浓度热力图"
    )

    # 保存
    filename = "heatmap_test.html"
    fig.write_html(filename)
    print(f"✅ 热力图已保存为: {filename}")
    return fig

def test_multiple_layers():
    """测试多图层地图"""
    print("🎯 测试多图层地图...")

    # 创建包含多个图层的地图
    fig = go.Figure()

    # 图层1：监测站
    stations_lat = [39.9042, 39.9142, 39.8942, 39.9242, 39.8842]
    stations_lon = [116.4074, 116.4174, 116.3974, 116.4274, 116.3874]
    stations_pm25 = [45, 78, 123, 34, 89]

    fig.add_trace(go.Scattermapbox(
        lat=stations_lat,
        lon=stations_lon,
        mode='markers',
        marker=dict(
            size=12,
            color=stations_pm25,
            colorscale='RdYlGn_r',
            showscale=True,
            colorbar=dict(title="PM2.5 (μg/m³)")
        ),
        text=[f'监测站 {i+1}<br>PM2.5: {pm25}' for i, pm25 in enumerate(stations_pm25)],
        hoverinfo='text',
        name="监测站"
    ))

    # 图层2：污染源
    sources_lat = [39.9100, 39.8900, 39.9200]
    sources_lon = [116.4200, 116.3900, 116.4300]

    fig.add_trace(go.Scattermapbox(
        lat=sources_lat,
        lon=sources_lon,
        mode='markers',
        marker=dict(
            size=15,
            color='red',
            symbol='square'
        ),
        text=['工厂A', '工厂B', '工厂C'],
        hoverinfo='text',
        name="污染源"
    ))

    fig.update_layout(
        mapbox=dict(
            style="open-street-map",
            center=dict(lat=39.9042, lon=116.4074),
            zoom=10
        ),
        height=600,
        title="多图层地图测试 - 监测站与污染源",
        showlegend=True
    )

    # 保存
    filename = "multiple_layers_test.html"
    fig.write_html(filename)
    print(f"✅ 多图层地图已保存为: {filename}")
    return fig

def test_performance():
    """测试地图性能"""
    print("⚡ 测试地图性能...")

    # 生成大量数据点
    n_points = 1000
    np.random.seed(42)

    center_lat, center_lon = 39.9042, 116.4074
    lats = center_lat + np.random.normal(0, 0.2, n_points)
    lons = center_lon + np.random.normal(0, 0.2, n_points)
    values = np.random.uniform(0, 200, n_points)

    start_time = datetime.now()

    fig = px.scatter_mapbox(
        lat=lats,
        lon=lons,
        color=values,
        size=values,
        hover_data={'lat': False, 'lon': False},
        color_continuous_scale='Viridis',
        size_max=10,
        zoom=8,
        center={'lat': center_lat, 'lon': center_lon},
        mapbox_style='open-street-map',
        title=f'性能测试 - {n_points} 个数据点'
    )

    fig.update_layout(height=600)

    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()

    # 保存
    filename = "performance_test.html"
    fig.write_html(filename)
    print(f"✅ 性能测试完成，处理时间: {processing_time:.2f}秒")
    print(f"✅ 性能测试地图已保存为: {filename}")
    return fig

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始地图渲染测试")
    print("=" * 50)

    test_results = {}

    try:
        # 测试1：基础地图
        test_results['basic'] = test_basic_map()
        print()

        # 测试2：区域填充地图
        test_results['choropleth'] = test_choropleth_map()
        print()

        # 测试3：散点地图
        test_results['scatter'] = test_scatter_mapbox()
        print()

        # 测试4：热力图
        test_results['heatmap'] = test_heatmap()
        print()

        # 测试5：多图层地图
        test_results['multiple'] = test_multiple_layers()
        print()

        # 测试6：性能测试
        test_results['performance'] = test_performance()
        print()

        print("🎉 所有测试完成！")
        print("📁 生成的测试文件:")
        for filename in [
            "basic_map_test.html",
            "choropleth_map_test.html",
            "scatter_mapbox_test.html",
            "heatmap_test.html",
            "multiple_layers_test.html",
            "performance_test.html"
        ]:
            if os.path.exists(filename):
                print(f"   ✅ {filename}")

        print("\n💡 提示: 双击任意HTML文件在浏览器中查看地图")

        # 询问是否打开浏览器
        try:
            choice = input("\n是否在浏览器中打开基础地图测试? (y/n): ").lower()
            if choice == 'y':
                webbrowser.open('basic_map_test.html')
        except:
            pass

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        print("请检查是否已安装所需依赖: pip install plotly pandas numpy")

    return test_results

def run_single_test(test_name):
    """运行单个测试"""
    tests = {
        'basic': test_basic_map,
        'choropleth': test_choropleth_map,
        'scatter': test_scatter_mapbox,
        'heatmap': test_heatmap,
        'multiple': test_multiple_layers,
        'performance': test_performance
    }

    if test_name in tests:
        print(f"🚀 运行单个测试: {test_name}")
        print("=" * 30)
        return tests[test_name]()
    else:
        print(f"❌ 未找到测试: {test_name}")
        print(f"可用测试: {list(tests.keys())}")
        return None

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")

    required_packages = ['plotly', 'pandas', 'numpy']
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    else:
        print("\n✅ 所有依赖都已安装")
        return True

if __name__ == "__main__":
    import sys

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    print()

    # 检查命令行参数
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        run_single_test(test_name)
    else:
        run_all_tests()
