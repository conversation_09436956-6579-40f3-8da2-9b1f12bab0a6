#!/usr/bin/env python3
"""
地理信息数据分析模块演示脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from geo_analysis import generate_geo_data, create_interactive_map, create_heatmap, spatial_analysis
import plotly.express as px

def main():
    print("🌍 地理信息数据分析模块演示")
    print("=" * 50)
    
    # 生成模拟数据
    print("📊 正在生成模拟地理数据...")
    stations_df, sources_df = generate_geo_data()
    
    print(f"✅ 数据生成完成:")
    print(f"   - 监测站数量: {len(stations_df)}")
    print(f"   - 污染源数量: {len(sources_df)}")
    
    # 显示数据样本
    print("\n📋 监测站数据样本:")
    print(stations_df[['name', 'latitude', 'longitude', 'PM2.5', 'AQI_level']].head())
    
    print("\n🏭 污染源数据样本:")
    print(sources_df[['name', 'source_type', 'emission_level', 'daily_emission']].head())
    
    # 进行空间分析
    print("\n📈 空间统计分析:")
    stats = spatial_analysis(stations_df, sources_df)
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"   - {key}: {value:.2f}")
        else:
            print(f"   - {key}: {value}")
    
    # 创建可视化图表
    print("\n🎨 正在创建可视化图表...")
    
    # 创建交互式地图
    map_fig = create_interactive_map(stations_df, sources_df, 'PM2.5')
    print("✅ 交互式地图创建完成")
    
    # 创建热力图
    heatmap_fig = create_heatmap(stations_df, 'PM2.5')
    print("✅ 污染物热力图创建完成")
    
    # 创建统计图表
    aqi_counts = stations_df['AQI_level'].value_counts()
    pie_fig = px.pie(
        values=aqi_counts.values,
        names=aqi_counts.index,
        title="空气质量等级分布"
    )
    print("✅ 统计图表创建完成")
    
    print("\n🚀 演示完成！")
    print("💡 提示: 运行 'streamlit run geo_visualization.py' 查看完整的Web界面")
    
    # 保存图表为HTML文件（可选）
    try:
        map_fig.write_html("geo_map_demo.html")
        heatmap_fig.write_html("heatmap_demo.html")
        pie_fig.write_html("stats_demo.html")
        print("📁 图表已保存为HTML文件:")
        print("   - geo_map_demo.html")
        print("   - heatmap_demo.html") 
        print("   - stats_demo.html")
    except Exception as e:
        print(f"⚠️  保存HTML文件时出错: {e}")

if __name__ == "__main__":
    main()
