import streamlit as st
import pandas as pd
import numpy as np
from pyecharts.charts import Line, Boxplot, HeatMap, Radar
from pyecharts import options as opts
from streamlit_echarts import st_pyecharts

# 数据加载
@st.cache_data
def load_data():
    return pd.read_csv('air_pollution_dataset.csv', parse_dates=['timestamp'])

# 折线图 - PM2.5时间趋势
def pm25_trend_chart(df):
    daily_pm25 = df.resample('D', on='timestamp')['PM2.5'].mean().reset_index()
    line = (
        Line()
        .add_xaxis(daily_pm25['timestamp'].dt.strftime('%Y-%m-%d').tolist())
        .add_yaxis('PM2.5浓度', daily_pm25['PM2.5'].round(2).tolist())
        .set_global_opts(
            title_opts=opts.TitleOpts(title="PM2.5时间趋势", subtitle='日均浓度变化'),
            tooltip_opts=opts.TooltipOpts(trigger="axis"),
            datazoom_opts=[opts.DataZoomOpts()]
        )
    )
    return line

# 箱线图 - 区域污染分布
def area_distribution_chart(df):
    box_data = [df[df['area_type'] == area]['PM2.5'].values.tolist() for area in df['area_type'].unique()]
    box = (
        Boxplot()
        .add_xaxis(df['area_type'].unique().tolist())
        .add_yaxis('PM2.5', box_data)
        .set_global_opts(
            title_opts=opts.TitleOpts(title="区域污染分布"),
            tooltip_opts=opts.TooltipOpts(trigger="item")
        )
    )
    return box

# 热力图 - 污染物相关性
def correlation_heatmap(df):
    corr = df[['PM2.5','PM10','SO2','NO2','CO','O3']].corr().round(2)
    heatmap = (
        HeatMap()
        .add_xaxis(corr.columns.tolist())
        .add_yaxis(
            "相关系数",
            corr.columns.tolist(),
            [[i,j,corr.iloc[i,j]] for i in range(len(corr)) for j in range(len(corr))],
            label_opts=opts.LabelOpts(is_show=True, position="inside")
        )
        .set_global_opts(
            title_opts=opts.TitleOpts(title="污染物相关性"),
            visualmap_opts=opts.VisualMapOpts(min_=-1, max_=1)
        )
    )
    return heatmap

# 雷达图 - 季节污染对比
def season_radar_chart(df):
    season_data = df.groupby('season')[['PM2.5','PM10','SO2','NO2','CO','O3']].mean()
    radar = (
        Radar()
        .add_schema(
            schema=[
                opts.RadarIndicatorItem(name="PM2.5", max_=season_data.max().max()),
                opts.RadarIndicatorItem(name="PM10", max_=season_data.max().max()),
                opts.RadarIndicatorItem(name="SO2", max_=season_data.max().max()),
                opts.RadarIndicatorItem(name="NO2", max_=season_data.max().max()),
                opts.RadarIndicatorItem(name="CO", max_=season_data.max().max()),
                opts.RadarIndicatorItem(name="O3", max_=season_data.max().max())
            ]
        )
        .add("冬季", [season_data.loc['winter'].tolist()],label_opts=opts.LabelOpts(is_show=False),color="#FF0000")
        .add("春季", [season_data.loc['spring'].tolist()],label_opts=opts.LabelOpts(is_show=False),color="#00FF00")
        .add("夏季", [season_data.loc['summer'].tolist()],label_opts=opts.LabelOpts(is_show=False),color="#FFFF00")
        .add("秋季", [season_data.loc['fall'].tolist()],label_opts=opts.LabelOpts(is_show=False),color="#0000FF")
        .set_global_opts(title_opts=opts.TitleOpts(title="季节污染对比"))
    )
    return radar

def main():
    st.set_page_config(page_title="污染数据分析", layout="wide")
    st.title("空气污染可视化分析")
    
    df = load_data()
    
    with st.container():
        col1, col2 = st.columns(2)
        with col1:
            st.subheader("PM2.5时间趋势")
            st_pyecharts(pm25_trend_chart(df), height=400)
        with col2:
            st.subheader("区域污染分布")
            st_pyecharts(area_distribution_chart(df), height=400)
    
    with st.container():
        st.subheader("污染物相关性")
        st_pyecharts(correlation_heatmap(df), height=500)
    
    with st.container():
        st.subheader("季节污染对比")
        st_pyecharts(season_radar_chart(df), height=500)

if __name__ == "__main__":
    main()