{"name": "django-vue3", "version": "3.0.4", "description": "前后端分离，后端采用django + django-rest-framework，前端采用基于 vue3 + CompositionAPI + typescript + vite + element plus", "license": "MIT", "scripts": {"dev": "vite --force", "build": "vite build", "build:local": "vite build --mode local_prod", "format": "prettier --write src/**/*.{js,jsx,vue}"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fast-crud/fast-crud": "^1.22.3", "@fast-crud/fast-extends": "^1.22.3", "@fast-crud/ui-element": "^1.22.3", "@fast-crud/ui-interface": "^1.22.3", "@iconify/vue": "^4.1.2", "@types/lodash": "^4.17.13", "@vitejs/plugin-vue-jsx": "^4.0.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "countup.js": "^2.8.0", "cropperjs": "^1.6.2", "e-icon-picker": "2.1.1", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.8.7", "element-tree-line": "^0.2.1", "font-awesome": "^4.7.0", "js-cookie": "^3.0.5", "js-table2excel": "^1.1.2", "jsplumb": "^2.15.6", "lodash-es": "^4.17.21", "marked": "^15.0.6", "mitt": "^3.0.1", "nprogress": "^0.2.0", "openai": "^4.80.0", "particles.vue3": "^2.12.0", "pinia": "^2.2.6", "pinia-plugin-persist": "^1.0.0", "postcss": "^8.4.47", "print-js": "^1.6.0", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.13.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.3", "splitpanes": "^3.1.5", "tailwindcss": "^3.4.14", "ts-md5": "^1.3.1", "tsparticles": "^3.5.0", "upgrade": "^1.1.0", "vue": "^3.5.12", "vue-clipboard3": "^2.0.0", "vue-cropper": "^1.0.8", "vue-echarts": "^7.0.3", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "^10.0.4", "vue-router": "^4.4.5", "vue3-particles": "^2.12.0", "vxe-table": "^4.6.23", "xe-utils": "^3.5.31"}, "devDependencies": {"@types/node": "^22.8.7", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.12.2", "@vitejs/plugin-vue": "^5.1.4", "@vue/compiler-sfc": "^3.5.12", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "prettier": "^3.4.2", "sass": "^1.63.2", "typescript": "^5.6.3", "vite": "^5.4.10", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.4.3"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "element-ui", "element-plus", "django-vue3-admin", "django", "django-restframework"], "repository": {"type": "git", "url": "https://gitee.com/huge-dream/django-vue3-admin.git"}}