
from .models import *
from rest_framework import serializers


class UserLoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField()


class RoleSerializer(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', allow_null=True, required=False)
    class Meta:
        model = Role
        fields = '__all__'


class ProfileSerializer(serializers.ModelSerializer):
    role_id = serializers.PrimaryKeyRelatedField(queryset=Role.objects.all(), source='role', allow_null=True, required=False)
    role = RoleSerializer(read_only=True)
    class Meta:
        model = Profile
        fields = '__all__'


class SwiperSerializer(serializers.ModelSerializer):
    class Meta:
        model = Swiper
        fields = '__all__'



class FeedbackSerializer(serializers.ModelSerializer):
    user_id = serializers.PrimaryKeyRelatedField(queryset=Profile.objects.all(), source='user', allow_null=True, required=False)
    user = ProfileSerializer(read_only=True)
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', allow_null=True, required=False)
    class Meta:
        model = Feedback
        fields = '__all__'


