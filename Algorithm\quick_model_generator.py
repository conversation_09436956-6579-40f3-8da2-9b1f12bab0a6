#!/usr/bin/env python3
"""
Quick model generator to create compatible model files for the current environment
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
import joblib
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def load_and_prepare_data():
    """Load the dataset and prepare it for training"""
    print("Loading dataset...")
    df = pd.read_csv('air_pollution_dataset.csv')
    print(f"Dataset loaded: {df.shape[0]} rows, {df.shape[1]} columns")
    
    # Define features and target
    exclude_cols = ['timestamp', 'date', 'PM2.5', 'AQI', 'AQI_category']
    feature_cols = [col for col in df.columns if col not in exclude_cols]
    
    # Convert date column if it exists
    if 'date' in df.columns:
        df['date'] = pd.to_datetime(df['date'])
    
    # Feature engineering
    if 'date' in df.columns:
        df['day_of_year'] = df['date'].dt.dayofyear
        df['week_of_year'] = df['date'].dt.isocalendar().week.astype(int)
        feature_cols.extend(['day_of_year', 'week_of_year'])
    
    # Add interaction features
    if 'temperature' in df.columns and 'humidity' in df.columns:
        df['temp_humidity'] = df['temperature'] * df['humidity'] / 100
        feature_cols.append('temp_humidity')
    
    if 'wind_speed' in df.columns and 'temperature' in df.columns:
        df['wind_temp_ratio'] = df['wind_speed'] / (df['temperature'] + 1)
        feature_cols.append('wind_temp_ratio')
    
    if 'industry_activity' in df.columns and 'traffic_flow' in df.columns:
        df['industry_traffic'] = df['industry_activity'] * df['traffic_flow'] / 100
        feature_cols.append('industry_traffic')
    
    print(f"Features to use: {len(feature_cols)} features")
    return df, feature_cols

def create_preprocessor(df, feature_cols):
    """Create preprocessing pipeline"""
    # Identify numeric and categorical features
    numeric_features = [col for col in feature_cols if df[col].dtype in ['float64', 'int64']]
    categorical_features = [col for col in feature_cols if col in ['area_type', 'season', 'time_type']]
    
    print(f"Numeric features: {len(numeric_features)}")
    print(f"Categorical features: {len(categorical_features)}")
    
    # Create preprocessing pipelines
    numeric_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='median')),
        ('scaler', StandardScaler())
    ])
    
    categorical_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='most_frequent')),
        ('onehot', OneHotEncoder(handle_unknown='ignore'))
    ])
    
    # Combine preprocessing steps
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', numeric_transformer, numeric_features),
            ('cat', categorical_transformer, categorical_features)
        ])
    
    return preprocessor

def train_simple_model(X_train, X_test, y_train, y_test):
    """Train a simple but effective model"""
    print("\nTraining Linear Regression model...")
    lr_model = LinearRegression()
    lr_model.fit(X_train, y_train)
    
    y_pred_lr = lr_model.predict(X_test)
    lr_r2 = r2_score(y_test, y_pred_lr)
    lr_rmse = np.sqrt(mean_squared_error(y_test, y_pred_lr))
    
    print(f"Linear Regression - R²: {lr_r2:.4f}, RMSE: {lr_rmse:.4f}")
    
    print("\nTraining Random Forest model...")
    rf_model = RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1)
    rf_model.fit(X_train, y_train)
    
    y_pred_rf = rf_model.predict(X_test)
    rf_r2 = r2_score(y_test, y_pred_rf)
    rf_rmse = np.sqrt(mean_squared_error(y_test, y_pred_rf))
    
    print(f"Random Forest - R²: {rf_r2:.4f}, RMSE: {rf_rmse:.4f}")
    
    # Choose the better model
    if rf_r2 > lr_r2:
        print(f"\nBest model: Random Forest (R² = {rf_r2:.4f})")
        return rf_model
    else:
        print(f"\nBest model: Linear Regression (R² = {lr_r2:.4f})")
        return lr_model

def main():
    """Main function to generate model files"""
    print("=== Quick Model Generator ===")
    
    # Load and prepare data
    df, feature_cols = load_and_prepare_data()
    
    # Create preprocessor
    preprocessor = create_preprocessor(df, feature_cols)
    
    # Prepare training data
    X = df[feature_cols]
    y = df['PM2.5']
    
    # Fit preprocessor and transform data
    X_processed = preprocessor.fit_transform(X)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X_processed, y, test_size=0.2, random_state=42
    )
    
    print(f"\nTraining set size: {X_train.shape[0]}")
    print(f"Test set size: {X_test.shape[0]}")
    
    # Train model
    best_model = train_simple_model(X_train, X_test, y_train, y_test)
    
    # Save model and preprocessor
    print("\nSaving model and preprocessor...")
    joblib.dump(best_model, 'best_model.pkl')
    joblib.dump(preprocessor, 'preprocessor.pkl')
    
    print("✅ Model files saved successfully!")
    print("- best_model.pkl")
    print("- preprocessor.pkl")
    
    # Test loading to ensure compatibility
    print("\nTesting model loading...")
    try:
        loaded_model = joblib.load('best_model.pkl')
        loaded_preprocessor = joblib.load('preprocessor.pkl')
        print("✅ Model files loaded successfully!")
        
        # Test prediction
        test_sample = X_test[:1]
        prediction = loaded_model.predict(test_sample)
        print(f"✅ Test prediction: {prediction[0]:.2f}")
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")

if __name__ == "__main__":
    main()
