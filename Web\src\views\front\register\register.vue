<template>
	<!-- 注册页面的主容器 -->
	<div class="w3l-signinform">
		<!-- 页面内容的包装容器 -->
		<div class="wrapper">
			<!-- 主要内容区域 -->
			<div class="w3l-form-info">
				<div class="w3_info">
					<!-- 标题和欢迎语 -->
					<h1>欢迎注册</h1>
					<p class="sub-para">欢迎使用我们的服务，请注册一个账户</p>
					<h2>注册</h2>

					<!-- 注册表单 -->
					<form @submit.prevent="register" class="form">
						<!-- 用户名输入框 -->
						<div class="input-group">
							<input v-model="registerForm.username" type="text" placeholder="请输入用户名" required />
						</div>

						<!-- 密码输入框 -->
						<div class="input-group two-groop">
							<input v-model="registerForm.password" type="password" placeholder="请输入密码" required />
						</div>

						<!-- 确认密码输入框 -->
						<div class="input-group three-groop">
							<input v-model="registerForm.password2" type="password" placeholder="确认密码" required />
						</div>

						<!-- 注册按钮 -->
						<button class="login-button" type="submit">注册</button>
					</form>

					<!-- 注册提示 -->
					<p class="account">已有账号？ <a @click="toLogin()" style="cursor: pointer">立即登录</a></p>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { reactive } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { ElMessage } from 'element-plus';

const router = useRouter();

const registerForm = reactive({
	username: '',
	password: '',
	password2: '',
});

// Check if the username already exists
const checkUsername = async (username) => {
	const res = await axios.post('/api/check-username/', { username });
	return res.data.message;
};

// Register function
const register = async () => {
	if (registerForm.password !== registerForm.password2) {
		ElMessage.error('两次输入密码不一致');
		return;
	}
	const usernameExists = await checkUsername(registerForm.username);
	if (usernameExists) {
		ElMessage.error('用户名已存在。请选择其他用户名。');
		return;
	}
	const res = await axios.post('/api/register/', {
		username: registerForm.username,
		password: registerForm.password,
	});

	if (res.data.message === '用户注册成功') {
		ElMessage.success('注册成功');
		toLogin();
	} else {
		ElMessage.error('注册失败');
	}
};

// Redirect to login page
const toLogin = () => {
	router.push({ path: '/login' });
};
</script>

<style scoped>
/* Main container */
.w3l-signinform {
	padding: 40px;
	min-height: 100vh;
	min-width: 100vw;
	background: url('./bg.jpg') no-repeat center;
	background-size: cover;
	-webkit-background-size: cover;
	-o-background-size: cover;
	-moz-background-size: cover;
	-ms-background-size: cover;
	position: relative;
	z-index: 1;
	display: flex;
	justify-content: center;
	align-items: center;
}

/* Form container */
.w3_info {
	background-color: white; /* White background for the form */
	max-width: 350px;
	width: 100%;
	padding: 25px 55px;
	border-radius: 20px;
	box-shadow: rgba(133, 189, 215, 0.**********) 0px 20px 30px -10px;
}

/* Heading and Sub-heading */
h1,
h2 {
	font-weight: 900;
	color: rgb(16, 137, 211);
}

h1 {
	font-size: 36px;
	margin-bottom: 0.4em;
}

h2 {
	font-size: 24px;
	margin-top: 20px;
}

.sub-para {
	font-size: 16px;
	margin-bottom: 20px;
	color: #555;
}

/* Input fields styling */
.input-group {
	position: relative;
	margin-top: 20px;
}

.input-group input {
	width: 100%;
	background: white;
	border: none;
	padding: 15px;
	border-radius: 20px;
	box-shadow: #cff0ff 0px 10px 10px -5px;
	border-inline: 2px solid transparent;
}

.input-group input:focus {
	outline: none;
	border-inline: 2px solid #12b1d1;
}

/* Account section */
.account {
	margin-top: 15px;
	font-size: 14px;
}

.account a {
	color: rgb(16, 137, 211);
}

.account a:hover {
	text-decoration: underline;
}

/* Register button */
.login-button {
	width: 100%;
	font-weight: bold;
	background: linear-gradient(45deg, rgb(16, 137, 211) 0%, rgb(18, 177, 209) 100%);
	color: white;
	padding: 15px;
	margin: 20px 0;
	border-radius: 20px;
	box-shadow: rgba(133, 189, 215, 0.**********) 0px 20px 10px -15px;
	border: none;
	cursor: pointer;
	transition: all 0.2s ease-in-out;
}

.login-button:hover {
	transform: scale(1.03);
	box-shadow: rgba(133, 189, 215, 0.**********) 0px 23px 10px -20px;
}

.login-button:active {
	transform: scale(0.95);
	box-shadow: rgba(133, 189, 215, 0.**********) 0px 15px 10px -10px;
}
</style>
