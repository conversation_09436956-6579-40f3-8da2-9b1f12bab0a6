# 地图测试脚本使用说明

## 简介

本目录包含两个地图测试脚本：

1. **`simple_map_test.py`** - 完整版地图测试脚本，使用 Plotly 库进行高级地图渲染
2. **`simple_map_test_basic.py`** - 基础版测试脚本，使用 HTML+Leaflet.js，无需额外依赖

## 快速开始

### 🚀 推荐：先运行基础测试

```bash
python simple_map_test_basic.py
```

这个脚本会：

- 检查 Python 环境
- 检查依赖包状态
- 生成基础 HTML 地图（使用 Leaflet.js）
- 创建测试报告
- 自动在浏览器中打开地图

### 📊 完整测试（需要安装依赖）

```bash
# 安装依赖
pip install plotly pandas numpy

# 运行完整测试
python simple_map_test.py
```

## 功能特性

### 🗺️ 基础测试脚本 (simple_map_test_basic.py)

**特点：**

- ✅ 无需安装额外依赖
- ✅ 使用 Leaflet.js + OpenStreetMap
- ✅ 生成独立的 HTML 文件
- ✅ 包含环境检查功能
- ✅ 自动生成测试报告

**功能：**

- 交互式地图（拖拽、缩放）
- 多个测试标记点
- 弹出信息窗口
- 圆形区域标记
- 环境诊断

### 🗺️ 完整测试脚本 (simple_map_test.py)

**测试类型：**

1. **基础地图测试** - 测试基本的散点地图渲染
2. **区域填充地图** - 测试世界地图的区域填充功能
3. **散点地图** - 测试带有数据可视化的散点地图
4. **热力图** - 测试密度热力图渲染
5. **多图层地图** - 测试多个数据层的叠加显示
6. **性能测试** - 测试大量数据点的渲染性能

### 📊 生成的文件

运行测试后会生成以下 HTML 文件：

- `basic_map_test.html` - 基础地图
- `choropleth_map_test.html` - 区域填充地图
- `scatter_mapbox_test.html` - 散点地图
- `heatmap_test.html` - 热力图
- `multiple_layers_test.html` - 多图层地图
- `performance_test.html` - 性能测试地图

## 安装依赖

在运行测试之前，请确保安装了所需的 Python 包：

```bash
pip install plotly pandas numpy
```

## 使用方法

### 运行所有测试

```bash
python simple_map_test.py
```

这将依次运行所有 6 个测试，并生成对应的 HTML 文件。

### 运行单个测试

您也可以运行特定的测试：

```bash
# 基础地图测试
python simple_map_test.py basic

# 区域填充地图测试
python simple_map_test.py choropleth

# 散点地图测试
python simple_map_test.py scatter

# 热力图测试
python simple_map_test.py heatmap

# 多图层地图测试
python simple_map_test.py multiple

# 性能测试
python simple_map_test.py performance
```

## 测试详情

### 1. 基础地图测试

- 创建简单的散点地图
- 显示北京市中心及周边测试点
- 使用 OpenStreetMap 作为底图

### 2. 区域填充地图测试

- 使用 Plotly 内置的世界人口数据
- 展示各国预期寿命分布
- 测试 choropleth 地图功能

### 3. 散点地图测试

- 生成 20 个模拟的空气质量监测站
- 根据 PM2.5 浓度进行颜色编码
- 包含悬停信息和图例

### 4. 热力图测试

- 生成网格化的污染浓度数据
- 使用密度热力图显示污染分布
- 以北京市中心为高值区域

### 5. 多图层地图测试

- 同时显示监测站和污染源
- 不同类型的标记和颜色
- 包含图例和交互功能

### 6. 性能测试

- 生成 1000 个随机数据点
- 测试大量数据的渲染性能
- 记录处理时间

## 故障排除

### 常见问题

1. **依赖包未安装**

   ```
   ModuleNotFoundError: No module named 'plotly'
   ```

   解决方案：运行 `pip install plotly pandas numpy`

2. **地图无法显示**

   - 检查网络连接（需要访问 OpenStreetMap）
   - 确保浏览器支持 JavaScript
   - 尝试使用不同的浏览器

3. **文件权限错误**
   - 确保当前目录有写入权限
   - 检查磁盘空间是否充足

### 性能优化建议

- 对于低性能设备，建议先运行单个测试
- 性能测试可能需要较长时间，请耐心等待
- 如果遇到内存不足，可以减少性能测试中的数据点数量

## 输出示例

### 基础测试输出

```
🚀 地图测试脚本 - 基础版本
==================================================
🐍 Python版本检查...
Python版本: 3.11.9 (main, Apr 12 2024, 09:55:31)  [GCC 13.2.0 64 bit (AMD64)]
Python路径: C:/msys64/mingw64/bin/python.exe
✅ Python版本符合要求

🔍 检查依赖...
❌ plotly - 未安装
❌ pandas - 未安装
❌ numpy - 未安装

⚠️  缺失的包: plotly, pandas, numpy
请运行以下命令安装:
pip install plotly pandas numpy

🗺️ 创建基础HTML地图...
✅ 基础HTML地图已保存为: basic_html_map_test.html

📊 生成测试报告...
✅ 测试报告已保存为: test_report.md

🎉 测试完成！
📁 生成的文件:
   ✅ basic_html_map_test.html
   ✅ test_report.md

💡 提示: 双击 basic_html_map_test.html 在浏览器中查看地图

是否在浏览器中打开地图? (y/n): y
🌐 已在浏览器中打开地图

⚠️  要运行完整的地图测试，请先安装: plotly pandas numpy
```

### 完整测试输出

```
🔍 检查依赖...
✅ plotly - 已安装
✅ pandas - 已安装
✅ numpy - 已安装

✅ 所有依赖都已安装

🚀 开始地图渲染测试
==================================================
🗺️ 测试基础地图渲染...
✅ 基础地图已保存为: basic_map_test.html

🌍 测试区域填充地图...
✅ 区域填充地图已保存为: choropleth_map_test.html

📍 测试散点地图...
✅ 散点地图已保存为: scatter_mapbox_test.html

🔥 测试热力图...
✅ 热力图已保存为: heatmap_test.html

🎯 测试多图层地图...
✅ 多图层地图已保存为: multiple_layers_test.html

⚡ 测试地图性能...
✅ 性能测试完成，处理时间: 0.85秒
✅ 性能测试地图已保存为: performance_test.html

🎉 所有测试完成！
📁 生成的测试文件:
   ✅ basic_map_test.html
   ✅ choropleth_map_test.html
   ✅ scatter_mapbox_test.html
   ✅ heatmap_test.html
   ✅ multiple_layers_test.html
   ✅ performance_test.html

💡 提示: 双击任意HTML文件在浏览器中查看地图

是否在浏览器中打开基础地图测试? (y/n):
```

## 技术说明

- 使用 Plotly 库进行地图渲染
- 支持多种地图类型和样式
- 生成的 HTML 文件包含完整的交互功能
- 兼容主流浏览器
- 无需额外的地图 API 密钥

## 扩展功能

您可以根据需要修改脚本：

- 调整数据点数量
- 更改地图中心位置
- 添加新的测试类型
- 自定义颜色方案
- 集成真实数据源

## 联系支持

如果遇到问题或需要帮助，请检查：

1. Python 版本（建议 3.7+）
2. 依赖包版本
3. 网络连接状态
4. 浏览器兼容性
