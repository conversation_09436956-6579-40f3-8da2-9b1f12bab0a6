
<!DOCTYPE html>
<html>
<head>
    <title>基础地图测试</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #map { height: 500px; width: 100%; border: 1px solid #ccc; }
        .info { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🗺️ 基础地图渲染测试</h1>
    <div class="info">
        <strong>测试信息:</strong><br>
        生成时间: 2025-05-28 14:23:04<br>
        地图类型: 基础交互式地图<br>
        技术栈: Leaflet.js + OpenStreetMap
    </div>
    
    <div id="map"></div>
    
    <div class="info">
        <strong>功能说明:</strong><br>
        • 可以拖拽移动地图<br>
        • 可以缩放地图<br>
        • 显示北京市中心及测试点<br>
        • 点击标记查看信息
    </div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        var map = L.map('map').setView([39.9042, 116.4074], 10);

        // 添加地图图层
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // 添加标记点
        var markers = [
            {lat: 39.9042, lng: 116.4074, title: "北京市中心", desc: "天安门广场附近"},
            {lat: 39.9142, lng: 116.4174, title: "测试点1", desc: "PM2.5: 45 μg/m³"},
            {lat: 39.8942, lng: 116.3974, title: "测试点2", desc: "PM2.5: 78 μg/m³"},
            {lat: 39.9242, lng: 116.4274, title: "测试点3", desc: "PM2.5: 32 μg/m³"},
            {lat: 39.8842, lng: 116.3874, title: "测试点4", desc: "PM2.5: 89 μg/m³"}
        ];

        // 添加标记到地图
        markers.forEach(function(marker) {
            L.marker([marker.lat, marker.lng])
                .addTo(map)
                .bindPopup('<b>' + marker.title + '</b><br>' + marker.desc);
        });

        // 添加圆圈表示影响范围
        L.circle([39.9042, 116.4074], {
            color: 'red',
            fillColor: '#f03',
            fillOpacity: 0.2,
            radius: 5000
        }).addTo(map).bindPopup("北京市中心区域");

        console.log("地图初始化完成！");
    </script>
</body>
</html>
