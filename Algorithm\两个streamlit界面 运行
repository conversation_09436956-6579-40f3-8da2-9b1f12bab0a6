
# 空气污染数据分析系统 - Streamlit服务启动说明

## 端口分配
- 8501: 数据可视化分析 (visual_analysis.py)
- 8502: 地理信息分析 (geo_visualization.py)
- 8503: 机器学习预测 (streamlit_ui.py)

## 启动命令

# 1. 机器学习预测服务 (端口8503)
streamlit run streamlit_ui.py --server.port 8503

# 2. 数据可视化分析服务 (端口8501)
streamlit run visual_analysis.py --server.port 8501

# 3. 地理信息分析服务 (端口8502)
streamlit run geo_visualization.py --server.port 8502

## 集成说明
- Web前端的"空气污染预测"页面对应端口8503
- Web前端的"数据可视化分析"页面集成了端口8501和8502的功能
- 用户可以在可视化分析页面中切换不同的分析模块