"""
Django settings for Backend project.

Generated by 'django-admin startproject' using Django 5.0.7.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""
import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-kl94b%3f(_*^mf#6%q5%vsti$g=7d*l_fp6s*bq2&bcye2$6yl"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['**********', 'localhost', '127.0.0.1', '0.0.0.0']

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",

    'App',                       # ++++++++++++++++++

    'corsheaders',                              # ++++++++++++++++++
    'rest_framework',                           # ++++++++++++++++++
    'rest_framework.authtoken',                 # ++++++++++++++++++
    'drf_yasg',                                 # ++++++++++++++++++

    'csp'                                       # ++++++++++++++++++
]

MIDDLEWARE = [

    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    'corsheaders.middleware.CorsMiddleware',                        # ++++++++++++++++++
    # "django.middleware.csrf.CsrfViewMiddleware",                  # ------------------
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    'csp.middleware.CSPMiddleware',                                 # ++++++++++++++++++

]

ROOT_URLCONF = "Backend.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "Backend.wsgi.application"

# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",
    }
}

# DATABASES = {
#     "default": {
#         "ENGINE": "django.db.backends.mysql",
#         "NAME": "your_database_name",  # 数据库名称
#         "USER": "your_database_user",  # 数据库用户名
#         "PASSWORD": "your_database_password",  # 数据库密码
#         "HOST": "localhost",  # 数据库主机，如果是本地数据库，使用 'localhost' 或 '127.0.0.1'
#         "PORT": "3306",  # 数据库端口，默认是 3306
#         "OPTIONS": {
#             "charset": "utf8mb4",  # 设置字符集
#         },
#     }
# }

# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'zh-Hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True

CONTENT_SECURITY_POLICY = {
    'DIRECTIVES': {
        'connect-src': ("'self'", 'http://localhost:8000', '*'),
        'default-src': ("'self'", '*'),
        'font-src': ("'self'", 'data:', 'https://fonts.gstatic.com', '*'),
        'frame-ancestors': ("'self'", 'http://127.0.0.1:5173', 'http://localhost:8000', '*'),
        'frame-src': ("'self'", 'http://127.0.0.1:7860', '*'),
        'img-src': ("'self'", 'data:', '*'),
        'media-src': ("'self'", '*'),
        'object-src': ("'none'", '*'),
        'script-src': ("'self'", 'blob:', "'unsafe-inline'", '*'),
        'style-src': ("'self'", "'unsafe-inline'", 'https://fonts.googleapis.com', '*'),
        'worker-src': ("'self'", 'blob:', '*')
    }
}


MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
MEDIA_URL = '/media/'


REST_FRAMEWORK = {
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
        # ...
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    # ...
}