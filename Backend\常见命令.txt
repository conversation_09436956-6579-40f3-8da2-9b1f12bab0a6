
列出conda环境
conda env list

创建conda环境:
conda create --name myenv python=x.xx -y

激活conda环境:
conda activate myenv

退出conda环境:
conda deactivate

删除conda环境：
conda remove --name myenv --all

更新pip:
python -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple

换源：
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

安装环境：
pip install -r requirements.txt

数据库迁移：
python manage.py makemigrations App
python manage.py migrate

创建django管理员：
python manage.py createsuperuser --username=admin --email=<EMAIL>

运行django:
python manage.py runserver


