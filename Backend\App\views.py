
import json
from .serializers import *
from django.db.models import Q
from rest_framework import status
from rest_framework import viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.generics import GenericAPIView
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth.hashers import check_password, make_password

class CustomPageNumberPagination(PageNumberPagination):
    page_size = 5
    page_size_query_param = 'page_size'
    max_page_size = 100


class CheckUsernameView(APIView):
    def post(self, request):
        username = request.data['username']
        exists = Profile.objects.filter(username=username).exists()
        return Response({"massage": exists}, status=status.HTTP_200_OK)


class RegisterView(APIView):
    def post(self, request):
        print(request.data)
        serializer = ProfileSerializer(data=request.data)
        if serializer.is_valid():
            serializer.validated_data['name'] = serializer.validated_data['username']
            password = make_password(serializer.validated_data['password'])
            serializer.validated_data['password'] = password
            user = serializer.save()

            return Response({
                'message': '用户注册成功',
                'user': ProfileSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LoginView(APIView):
    def post(self, request):
        print(request.data)
        serializer = UserLoginSerializer(data=request.data)
        if serializer.is_valid():
            username = serializer.validated_data['username']
            password = serializer.validated_data['password']
            try:
                user = Profile.objects.filter(username=username).first()
                if check_password(password, user.password):
                    file_path = "media/user_info.json"
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(ProfileSerializer(user).data, f, ensure_ascii=False, indent=4)
                    return Response({
                        'message': '登录成功',
                        'user': ProfileSerializer(user).data
                    }, status=status.HTTP_200_OK)
                else:
                    return Response({'message': '密码错误'}, status=status.HTTP_200_OK)
            except Exception:
                return Response({'message': '用户不存在'}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_200_OK)


class SettingPasswordView(APIView):
    def post(self, request):
        print(request.data)
        user = Profile.objects.get(id=request.data['id'])
        print(request.data['oldPassword'])
        if check_password(request.data['oldPassword'], user.password):
            user.password = make_password(request.data['newPassword'])
            print(make_password(request.data['newPassword']))
            user.save()
            return Response({
                'message': '修改成功',
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'message': '旧密码错误',
            }, status=status.HTTP_401_UNAUTHORIZED)

class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all().order_by('id')
    serializer_class = RoleSerializer
    pagination_class = CustomPageNumberPagination

class ProfileViewSet(viewsets.ModelViewSet):
    queryset = Profile.objects.all().order_by('id')
    serializer_class = ProfileSerializer
    pagination_class = CustomPageNumberPagination


    def get_queryset(self):
        queryset = super().get_queryset()
        filter_conditions = Q()
        for field in ['role_id']:
            value = self.request.query_params.get(field)
            if value:
                filter_conditions &= Q(**{f"{field}": value})
        return queryset.filter(filter_conditions)

class SwiperPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'

class SwiperViewSet(viewsets.ModelViewSet):
    queryset = Swiper.objects.all().order_by('id')
    serializer_class = SwiperSerializer
    pagination_class = SwiperPagination



class FeedbackViewSet(viewsets.ModelViewSet):
    queryset = Feedback.objects.all().order_by('id')
    serializer_class = FeedbackSerializer
    pagination_class = CustomPageNumberPagination

    def get_queryset(self):
        queryset = super().get_queryset()
        filter_conditions = Q()
        for field in ['user_id']:
            value = self.request.query_params.get(field)
            if value:
                filter_conditions &= Q(**{f"{field}": value})
        return queryset.filter(filter_conditions)



def get_queryset(data, table):
    if data:
        q_objects = Q()
        for key, value in data.items():
            q_objects &= Q(**{f'{key}__icontains': value})
        queryset = table.objects.filter(q_objects)
    else:
        queryset = table.objects.all()
    return queryset


class RoleSearchView(GenericAPIView):
    pagination_class = CustomPageNumberPagination
    serializer_class = RoleSerializer
    def post(self, request):
        queryset = get_queryset(request.data,Role)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
class ProfileSearchView(GenericAPIView):
    pagination_class = CustomPageNumberPagination
    serializer_class = ProfileSerializer
    def post(self, request):
        queryset = get_queryset(request.data,Profile)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class SwiperSearchView(GenericAPIView):
    pagination_class = CustomPageNumberPagination
    serializer_class = SwiperSerializer
    def post(self, request):
        queryset = get_queryset(request.data,Swiper)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)



class FeedbackSearchView(GenericAPIView):
    pagination_class = CustomPageNumberPagination
    serializer_class = FeedbackSerializer
    def post(self, request):
        queryset = get_queryset(request.data,Feedback)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)



# import threading
# from App.handle.Ai_ui import run_gradio_ui2
# thread2 = threading.Thread(target=run_gradio_ui2)
# thread2.daemon = True
# thread2.start()

#import threading
#from App.handle.app import run_gradio_ui
#thread2 = threading.Thread(target=run_gradio_ui)
#thread2.daemon = True
#thread2.start()