import streamlit as st
import pandas as pd
import joblib

# 加载模型和预处理器
@st.cache_resource
def load_model_and_preprocessor():
    model = joblib.load('best_model.pkl')
    preprocessor = joblib.load('preprocessor.pkl')  # 确保训练时已保存
    return model, preprocessor

# 预测函数
def predict_pm25(model, preprocessor, input_data):
    # 将输入数据转换为 DataFrame，并确保列名与训练时一致
    input_df = pd.DataFrame([input_data])
    # 预处理输入数据
    X_processed = preprocessor.transform(input_df)
    prediction = model.predict(X_processed)[0]
    return prediction

# Streamlit 界面
def main():
    # 加载模型和预处理器
    model, preprocessor = load_model_and_preprocessor()

    # 自定义 CSS 美化
    st.markdown("""
    <style>
    .main {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 30px;
        border-radius: 15px;
        color: #000;
        min-height: 100vh;
    }

    /* 页面容器样式 */
    .stApp {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* 标题区域样式 */
    .title {
        color: #2c3e50;
        font-size: 36px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    .subtitle {
        color: #34495e;
        font-size: 18px;
        text-align: center;
        margin-bottom: 30px;
        font-weight: 300;
    }

    /* 表单区域样式 */
    .form-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        margin: 20px 0;
        border: 1px solid rgba(255,255,255,0.2);
    }

    .section-title {
        color: #2c3e50;
        font-size: 20px;
        font-weight: 600;
        text-align: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #3498db;
    }

    /* 按钮样式优化 */
    .stButton>button {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        border: none;
        padding: 18px 40px;
        border-radius: 30px;
        font-size: 18px;
        font-weight: bold;
        width: 100%;
        box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    .stButton>button:hover {
        background: linear-gradient(45deg, #2980b9, #3498db);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(52, 152, 219, 0.6);
    }

    /* 输入框样式优化 */
    .stTextInput>div>input, .stNumberInput>div>input, .stSelectbox>div>select {
        border-radius: 10px;
        border: 2px solid #e0e6ed;
        padding: 12px 16px;
        font-size: 14px;
        height: 45px;
        background-color: #ffffff;
        transition: all 0.3s ease;
        color: #2c3e50;
    }

    /* 标签样式 */
    .stNumberInput>div>label, .stSelectbox>div>label, .stTextInput>div>label {
        color: #000000 !important;
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 8px;
        text-transform: capitalize;
    }

    /* 输入框布局优化 */
    .stNumberInput, .stSelectbox, .stTextInput {
        margin-bottom: 15px;
    }
    .stNumberInput > div, .stSelectbox > div, .stTextInput > div {
        margin-bottom: 0;
    }

    /* 输入框聚焦效果 */
    .stTextInput>div>input:focus, .stNumberInput>div>input:focus, .stSelectbox>div>select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        outline: none;
        background-color: #f8f9ff;
    }

    /* 确保所有标签都是黑色 */
    label {
        color: #000000 !important;
    }

    /* 结果显示区域 */
    .result-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 30px;
        border-radius: 20px;
        margin: 30px 0;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .result {
        font-size: 28px;
        color: #ffffff;
        text-align: center;
        margin: 10px 0;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .aqi-result {
        font-size: 20px;
        text-align: center;
        margin-top: 15px;
        font-weight: 600;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .title {
            font-size: 28px;
        }
        .subtitle {
            font-size: 16px;
        }
        .form-section {
            padding: 20px;
        }
    }
    </style>
    """, unsafe_allow_html=True)

    # 标题和简介
    st.markdown('<div class="title">PM2.5 浓度预测</div>', unsafe_allow_html=True)
    st.markdown('<div class="subtitle">输入环境数据，预测空气中的 PM2.5 浓度</div>', unsafe_allow_html=True)

    # 输入表单布局 - 分组显示
    st.markdown('<div class="section-title">📊 输入预测数据</div>', unsafe_allow_html=True)

    # 创建表单容器
    with st.container():
        st.markdown('<div class="form-section">', unsafe_allow_html=True)

        # 时间信息组
        st.markdown("**⏰ 时间信息**")
        col1, col2 = st.columns(2)
        with col1:
            hour = st.number_input("小时", min_value=0, max_value=23, value=12, help="0-23小时制")
            month = st.number_input("月份", min_value=1, max_value=12, value=8, help="1-12月")
        with col2:
            day_of_week = st.number_input("星期几", min_value=0, max_value=6, value=3, help="0=周一, 6=周日")
            is_weekend = st.selectbox("是否周末", [0, 1], index=0, help="0=工作日, 1=周末")

        st.markdown("---")

        # 气象信息组
        st.markdown("**🌤️ 气象信息**")
        col1, col2 = st.columns(2)
        with col1:
            temperature = st.number_input("温度 (°C)", min_value=-10.0, max_value=50.0, value=25.0)
            wind_speed = st.number_input("风速 (m/s)", min_value=0.0, max_value=15.0, value=3.0)
            atmospheric_pressure = st.number_input("大气压 (hPa)", min_value=980.0, max_value=1040.0, value=1013.0)
        with col2:
            humidity = st.number_input("湿度 (%)", min_value=0.0, max_value=100.0, value=60.0)
            precipitation = st.number_input("降水量 (mm)", min_value=0.0, max_value=50.0, value=0.0)

        st.markdown("---")

        # 污染物信息组
        st.markdown("**🏭 污染物浓度**")
        col1, col2 = st.columns(2)
        with col1:
            pm10 = st.number_input("PM10 (μg/m³)", min_value=0.0, max_value=500.0, value=70.0)
            no2 = st.number_input("NO2 (μg/m³)", min_value=0.0, max_value=200.0, value=40.0)
            o3 = st.number_input("O3 (μg/m³)", min_value=0.0, max_value=250.0, value=60.0)
        with col2:
            so2 = st.number_input("SO2 (μg/m³)", min_value=0.0, max_value=150.0, value=15.0)
            co = st.number_input("CO (mg/m³)", min_value=0.0, max_value=10.0, value=1.0)

        st.markdown("---")

        # 环境信息组
        st.markdown("**🏙️ 环境信息**")
        col1, col2 = st.columns(2)
        with col1:
            industry_activity = st.number_input("工业活动指数", min_value=0.0, max_value=100.0, value=50.0)
            area_type = st.selectbox("区域类型", ['industrial', 'urban', 'suburban', 'rural'],
                                   index=1, help="工业区/城市/郊区/农村")
            time_type = st.selectbox("时间类型", ['weekday_peak', 'weekday_normal', 'weekend'],
                                   index=1, help="工作日高峰/工作日正常/周末")
        with col2:
            traffic_flow = st.number_input("交通流量指数", min_value=0.0, max_value=100.0, value=60.0)
            season = st.selectbox("季节", ['winter', 'spring', 'summer', 'fall'],
                                index=2, help="冬季/春季/夏季/秋季")
            aqi_category = st.selectbox("AQI类别", ['优', '良', '轻度污染', '中度污染', '重度污染', '严重污染'],
                                      index=1, help="当前空气质量等级")

        st.markdown('</div>', unsafe_allow_html=True)

    # 计算特征工程字段（与训练时一致）
    day_of_year = pd.Timestamp(f"2023-{month}-01").dayofyear
    week_of_year = pd.Timestamp(f"2023-{month}-01").isocalendar().week
    temp_humidity = temperature * humidity / 100
    wind_temp_ratio = wind_speed / (temperature + 1)
    industry_traffic = industry_activity * traffic_flow / 100
    pm_ratio = pm10 / (pm10 + 1)  # 使用 PM10 近似，因为 PM2.5 是预测目标
    no2_so2_ratio = no2 / (so2 + 1)
    weather_index = (temperature - humidity / 10 + wind_speed * 3 - precipitation * 2) / 10

    # 输入数据字典（确保与训练时的 feature_cols 一致）
    input_data = {
        'hour': hour,
        'day_of_week': day_of_week,
        'is_weekend': is_weekend,
        'month': month,
        'temperature': temperature,
        'humidity': humidity,
        'wind_speed': wind_speed,
        'precipitation': precipitation,
        'atmospheric_pressure': atmospheric_pressure,
        'industry_activity': industry_activity,
        'traffic_flow': traffic_flow,
        'PM10': pm10,
        'SO2': so2,
        'NO2': no2,
        'CO': co,
        'O3': o3,
        'day_of_year': day_of_year,
        'week_of_year': week_of_year,
        'temp_humidity': temp_humidity,
        'wind_temp_ratio': wind_temp_ratio,
        'industry_traffic': industry_traffic,
        'pm_ratio': pm_ratio,
        'no2_so2_ratio': no2_so2_ratio,
        'weather_index': weather_index,
        'area_type': area_type,
        'season': season,
        'time_type': time_type,
        'AQI_category': aqi_category
    }

    # 预测按钮 - 居中显示
    st.markdown("<br><br>", unsafe_allow_html=True)
    _, col_center, _ = st.columns([1, 1, 1])
    with col_center:
        if st.button("预测 PM2.5", use_container_width=True):
            try:
                prediction = predict_pm25(model, preprocessor, input_data)
                st.markdown(f'<div class="result">预测 PM2.5 浓度: {prediction:.2f} μg/m³</div>', unsafe_allow_html=True)

                # 添加AQI等级判断
                if prediction <= 35:
                    aqi_level = "优"
                    color = "#00e400"
                elif prediction <= 75:
                    aqi_level = "良"
                    color = "#ffff00"
                elif prediction <= 115:
                    aqi_level = "轻度污染"
                    color = "#ff7e00"
                elif prediction <= 150:
                    aqi_level = "中度污染"
                    color = "#ff0000"
                elif prediction <= 250:
                    aqi_level = "重度污染"
                    color = "#8f3f97"
                else:
                    aqi_level = "严重污染"
                    color = "#7e0023"

                st.markdown(f'<div style="text-align: center; font-size: 18px; color: {color}; margin-top: 10px;">空气质量等级: {aqi_level}</div>', unsafe_allow_html=True)

            except Exception as e:
                st.error(f"预测出错: {str(e)}")

if __name__ == "__main__":
    main()