
<template>
<div class="app-container">
    <header class="app-header">
        <div class="logo">
            <div class="logo-link" style="font-weight: bold">{{themeConfig.globalTitle}}</div>
        </div>
        <nav class="navigation">
            <ul class="nav-list">
                <li v-for="item in navItems" :key="item.name" class="nav-item">
                    <router-link :to="item.path" :class="{ active: isActive(item) }" class="nav-link">{{item.meta.title}} </router-link>
                </li>
            </ul>
        </nav>
        <div class="admin-link">
            <router-link to="/home" class="admin-link-text">后台管理</router-link>
        </div>
    </header>
    <main class="content-area">

            <div class="carousel-container">
                <el-carousel height="600px" motion-blur>
                    <el-carousel-item v-for="item in swiperData" :key="item">
                        <div class="image-container">
                            <img :src="item.image" alt="图片" class="full-image" />
                        </div>
                    </el-carousel-item>
                </el-carousel>
            </div>

        <!-- 路由出口 -->
        <router-view style="margin: 20px"/>
    </main>
</div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import {useThemeConfig} from '/@/stores/themeConfig';
import { onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';
const storesThemeConfig = useThemeConfig();
const {themeConfig} = storeToRefs(storesThemeConfig);


// 定义导航项
const navItems = [
//8501
// { path: '/main-streamlit', name: 'streamlit', meta: {title: 'streamlit' } },
//7860
// { path: '/main-gradio', name: 'gradio', meta: {title: 'gradio' } },
{ path: '/main-Streamlit', name: 'Streamlit', meta: {title: '空气污染预测' } },
{ path: '/main-Streamlit1', name: 'Streamlit1', meta: {title: '数据可视化分析' } },
{ path: '/main-ai', name: 'detail', meta: {title: 'ai问答' } },
{ path: '/main-feedback', name: 'feedback', meta: {title: '系统反馈' } },

    // { path: '/main-algorithm', name: 'algorithm', meta: {title: '算法界面' } },

];

// 获取当前路由对象
const route = useRoute();

// 计算属性来判断是否为激活状态
const isActive = computed(() => (item) => {
return route.path.startsWith(item.path);
});

const swiperData = ref([]);
const fetchData = async () => {
    try {
            const response =
    await axios.get('/api/swiper/', );
    swiperData.value = response.data.results;
    } catch (error) {
    ElMessage.error('数据加载失败');
    }
};

onMounted(() => {

        fetchData();

});

</script>
<style scoped>
.app-header {
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background-color: #ffffff;
  color: #333333;
  font-size: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  border-radius: 10px; /* 圆角 */
}

.logo-link {
  font-weight: bold;
  font-size: 1.2rem;
}

.nav-list {
  list-style-type: none;
  display: flex;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-right: 25px;
}

.nav-link {
  color: #333;
  text-decoration: none;
  font-weight: normal;
  padding: 5px 10px;
  border-radius: 5px; /* 圆角 */
  transition: background-color 0.3s;
}

.nav-link:hover {
  background-color: #f1f1f1; /* 悬停时背景变浅 */
}

.nav-link.active {
  font-weight: bold;
  color: #007bff; /* 激活状态的颜色 */
}

.admin-link-text {
  color: #007bff;
  font-weight: bold;
  text-decoration: none;
}

.admin-link-text:hover {
  color: #0056b3;
}
.content-area {
  flex-grow: 1;
  background-color: #f9f9f9;
  height: 90vh;
  overflow-y: scroll;
}
.full-image {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 保持图片比例，填满容器 */
}
</style>