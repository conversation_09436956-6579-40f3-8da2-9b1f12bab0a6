# Generated by Django 5.1.7 on 2025-03-21 12:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Profile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        unique=True,
                        verbose_name="用户名",
                    ),
                ),
                (
                    "password",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="密码",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="姓名",
                    ),
                ),
                (
                    "gender",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="性别",
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        default="avatars/default.png",
                        null=True,
                        upload_to="avatars/",
                        verbose_name="头像",
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="手机号",
                    ),
                ),
                (
                    "email",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="邮箱",
                    ),
                ),
                (
                    "dept",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="所属部门",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="备注"
                    ),
                ),
            ],
            options={
                "verbose_name": "用户信息",
                "verbose_name_plural": "用户信息",
                "db_table": "Profile",
            },
        ),
        migrations.CreateModel(
            name="Role",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "role_name",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="角色名称",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="角色描述"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="创建时间"
                    ),
                ),
            ],
            options={
                "verbose_name": "角色",
                "verbose_name_plural": "角色",
                "db_table": "Role",
            },
        ),
        migrations.CreateModel(
            name="Swiper",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="标题",
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="swiper/images/",
                        verbose_name="图片",
                    ),
                ),
                (
                    "location",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="标题位置",
                    ),
                ),
            ],
            options={
                "verbose_name": "轮播图",
                "verbose_name_plural": "轮播图",
                "db_table": "Swiper",
            },
        ),
        migrations.CreateModel(
            name="Feedback",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "subject",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="反馈主题",
                    ),
                ),
                (
                    "message",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="反馈信息"
                    ),
                ),
                (
                    "resolved",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="是否已解决",
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="feedback/images/",
                        verbose_name="配图",
                    ),
                ),
                (
                    "video",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="feedback/videos/",
                        verbose_name="视频",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="反馈时间"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="feedbacks",
                        to="App.profile",
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "系统反馈",
                "verbose_name_plural": "系统反馈",
                "db_table": "Feedback",
            },
        ),
        migrations.AddField(
            model_name="profile",
            name="role",
            field=models.ForeignKey(
                blank=True,
                default=1,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="profiles",
                to="App.role",
                verbose_name="角色",
            ),
        ),
    ]
