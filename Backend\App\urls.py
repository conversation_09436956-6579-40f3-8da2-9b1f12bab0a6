from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views import *

router = DefaultRouter()
router.register(r'role', RoleViewSet)
router.register(r'profile', ProfileViewSet)
router.register(r'swiper', SwiperViewSet)
router.register(r'feedback', FeedbackViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('search/role/', RoleSearchView.as_view()),
    path('search/profile/', ProfileSearchView.as_view()),
    path('search/swiper/', SwiperSearchView.as_view()),
    path('search/feedback/', FeedbackSearchView.as_view()),

    path('check-username/', CheckUsernameView.as_view(), name='check-username'),
    path('register/', RegisterView.as_view(), name='register'),
    path('login/', LoginView.as_view(), name='login'),
    path('setting-password/', SettingPasswordView.as_view(), name='setting-password'),
]
