<template>
  <div class="chat-container">
    <h1 style="font-size: 20px">AI 客服问答</h1>
    <div class="chat-window">
      <div v-for="(message, index) in messages" :key="index" :class="['message', message.role]">
        <div class="bubble" v-html="formatMessage(message.content)"></div>
      </div>
      <div v-if="loading" class="message ai">
        <div class="bubble">加载中...</div>
      </div>
    </div>
    <div class="input-container">
      <input v-model="userInput" placeholder="请输入你的问题" @keyup.enter="askAI" />
      <button @click="askAI">发送</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import OpenAI from 'openai';
import { marked } from 'marked';
const userInput = ref('');
const messages = ref([
  { role: 'system', content: '问答助手' }, // 系统初始化消息
]);
const loading = ref(false);

// 初始化 OpenAI 客户端
const openai = new OpenAI({
  baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1', // DeepSeek 的 API 地址
  apiKey: '***********************************', // 替换为你的 DeepSeek API Key
  dangerouslyAllowBrowser: true
});



const formatMessage = (content) => {
  return marked.parse(content); // 将 Markdown 转换为 HTML
};

// 向 AI 提问
const askAI = async () => {
  if (!userInput.value.trim()) return;

  // 添加用户消息
  messages.value.push({ role: 'user', content: userInput.value });
  const question = userInput.value;
  userInput.value = '';
  loading.value = true;

  try {
    // 调用 DeepSeek API
    const completion = await openai.chat.completions.create({
      messages: messages.value, // 发送所有历史消息
      model: "qwen-max", // 使用的模型
    });

    // 添加 AI 回复
    const aiResponse = completion.choices[0].message.content;
    messages.value.push({role: 'assistant', content: aiResponse});
  } catch (err) {
    messages.value.push({role: 'assistant', content: '请求失败，请稍后重试'});
    console.error(err);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.chat-container {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 60vh;
  background-color: #f9f9f9;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h1 {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.chat-window {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message {
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.bubble {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 15px;
  word-wrap: break-word;
}

.message.user .bubble {
  background-color: #007bff;
  color: white;
}

.message.assistant .bubble {
  background-color: #e1e1e1;
  color: #333;
}

.input-container {
  display: flex;
  gap: 10px;
}

input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

button:hover {
  background-color: #0056b3;
}
</style>