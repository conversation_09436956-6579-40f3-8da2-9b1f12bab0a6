# 空气污染数据分析系统 - 完整启动指南

## 系统环境要求
- Python 3.11+ 
- 已安装所有依赖包（通过 requirements.txt）

## 依赖安装
在运行应用之前，请确保已安装所有依赖：
```bash
pip install -r requirements.txt
```

## 应用启动命令

### 方法一：使用完整路径启动（推荐）

#### 1. 地理信息分析服务 (端口8502)
```bash
cd "C:/Users/<USER>/Desktop/8581-基于机器学习的空气污染数据分析系统/Algorithm" && C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe -m streamlit run geo_visualization.py --server.port 8502
```

#### 2. 数据可视化分析服务 (端口8501)
```bash
cd "C:/Users/<USER>/Desktop/8581-基于机器学习的空气污染数据分析系统/Algorithm" && C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe -m streamlit run visual_analysis.py --server.port 8501
```

#### 3. 机器学习预测服务 (端口8503)
```bash
cd "C:/Users/<USER>/Desktop/8581-基于机器学习的空气污染数据分析系统/Algorithm" && C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe -m streamlit run streamlit_ui.py --server.port 8503
```

### 方法二：在Algorithm目录下直接启动

首先切换到项目目录：
```bash
cd "C:/Users/<USER>/Desktop/8581-基于机器学习的空气污染数据分析系统/Algorithm"
```

然后分别启动各个服务：

#### 1. 地理信息分析服务 (端口8502)
```bash
C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe -m streamlit run geo_visualization.py --server.port 8502
```

#### 2. 数据可视化分析服务 (端口8501)
```bash
C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe -m streamlit run visual_analysis.py --server.port 8501
```

#### 3. 机器学习预测服务 (端口8503)
```bash
C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe -m streamlit run streamlit_ui.py --server.port 8503
```

### 方法三：使用批处理文件启动（最简单）

创建以下批处理文件来快速启动：

#### start_geo.bat - 启动地理信息分析
```batch
@echo off
cd /d "C:\Users\<USER>\Desktop\8581-基于机器学习的空气污染数据分析系统\Algorithm"
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m streamlit run geo_visualization.py --server.port 8502
pause
```

#### start_visual.bat - 启动数据可视化分析
```batch
@echo off
cd /d "C:\Users\<USER>\Desktop\8581-基于机器学习的空气污染数据分析系统\Algorithm"
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m streamlit run visual_analysis.py --server.port 8501
pause
```

#### start_ml.bat - 启动机器学习预测
```batch
@echo off
cd /d "C:\Users\<USER>\Desktop\8581-基于机器学习的空气污染数据分析系统\Algorithm"
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m streamlit run streamlit_ui.py --server.port 8503
pause
```

#### start_all.bat - 同时启动所有服务
```batch
@echo off
echo 正在启动空气污染数据分析系统...
cd /d "C:\Users\<USER>\Desktop\8581-基于机器学习的空气污染数据分析系统\Algorithm"

echo 启动地理信息分析服务 (端口8502)...
start "地理信息分析" C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m streamlit run geo_visualization.py --server.port 8502

timeout /t 3 /nobreak >nul

echo 启动数据可视化分析服务 (端口8501)...
start "数据可视化分析" C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m streamlit run visual_analysis.py --server.port 8501

timeout /t 3 /nobreak >nul

echo 启动机器学习预测服务 (端口8503)...
start "机器学习预测" C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m streamlit run streamlit_ui.py --server.port 8503

echo 所有服务已启动完成！
echo.
echo 访问地址：
echo 地理信息分析: http://localhost:8502
echo 数据可视化分析: http://localhost:8501
echo 机器学习预测: http://localhost:8503
echo.
pause
```

## 访问地址

启动成功后，可以通过以下地址访问各个服务：

- **地理信息分析**: http://localhost:8502
- **数据可视化分析**: http://localhost:8501  
- **机器学习预测**: http://localhost:8503

## 端口分配说明

- 8501: 数据可视化分析 (visual_analysis.py)
- 8502: 地理信息分析 (geo_visualization.py)
- 8503: 机器学习预测 (streamlit_ui.py)

## 故障排除

### 1. 如果遇到"模块未找到"错误
确保使用正确的Python解释器路径，并且已安装所有依赖：
```bash
C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe -m pip install -r requirements.txt
```

### 2. 如果遇到"文件未找到"错误
确保在正确的目录下运行命令，所有数据文件（如air_pollution_dataset.csv、best_model.pkl等）都应该在Algorithm目录中。

### 3. 如果端口被占用
可以修改端口号，例如：
```bash
--server.port 8504
```

### 4. 如果需要外网访问
添加以下参数：
```bash
--server.address 0.0.0.0
```

## 注意事项

1. 确保所有必需的数据文件都在Algorithm目录中
2. 每个服务使用不同的端口，避免冲突
3. 建议使用批处理文件来简化启动过程
4. 如果系统环境变量中的Python路径不同，请相应调整命令中的Python路径

## 集成说明

- Web前端的"空气污染预测"页面对应端口8503
- Web前端的"数据可视化分析"页面集成了端口8501和8502的功能
- 用户可以在可视化分析页面中切换不同的分析模块
